# Analytics Pages Spacing Standards

This document outlines the standardized spacing conventions implemented across all analytics pages to match the Dashboard page standards.

## Overall Container Structure

All analytics pages use the same container structure:
```tsx
<div className="space-y-6">
  {/* Page content with 12-column grid */}
</div>
```

## Grid Layout System

All analytics pages implement a 12-column grid system with consistent gap spacing:
```tsx
<div className="grid grid-cols-12 gap-6">
  {/* Content cards spanning various column widths */}
</div>
```

## Card Styling Standards

All cards across analytics pages use consistent styling:
```tsx
<Card className="shadow-md border border-gray-200 bg-white rounded-lg">
  <CardHeader>
    <CardTitle className="text-lg">Card Title</CardTitle>
    <CardDescription>Card description</CardDescription>
  </CardHeader>
  <CardContent>
    {/* Card content */}
  </CardContent>
</Card>
```

## KPI/Metric Cards

Metric cards use a consistent responsive layout:
```tsx
<div className="col-span-12 md:col-span-3">
  <MetricCard
    title="Metric Title"
    value={metricValue}
    subtitle="Metric description"
    icon={<IconComponent />}
  />
</div>
```

## Responsive Breakpoints

All analytics pages follow the same responsive design patterns:
- Mobile: 12 columns (full width)
- Tablet: 12 columns (full width)
- Desktop: Variable column spans (3, 4, 6, 8, 12)

## Specific Page Implementations

### Dashboard Page (Reference)
- Uses 12-column grid with gap-6
- KPI cards: col-span-12 md:col-span-3
- Charts: col-span-12 lg:col-span-8
- Side panels: col-span-12 lg:col-span-4

### Expense Analytics Page
- Matches dashboard grid system
- Uses standardized card components
- Consistent spacing between sections

### Product Analytics Page
- Implements 12-column grid layout
- Uses standardized card styling
- Responsive column spans for different components

### Sales Analytics Page
- Follows 12-column grid with gap-6
- Standardized card components with shadow-md, border-gray-200, bg-white, rounded-lg
- Consistent responsive behavior

### Profit & Loss Report Page
- Updated to use 12-column grid system
- Standardized card components with consistent styling
- Proper spacing between elements

## CSS Classes Used

### Container Classes
- `space-y-6` - For vertical spacing between major sections
- `grid grid-cols-12 gap-6` - For grid layout with consistent gaps

### Card Classes
- `shadow-md` - Consistent shadow depth
- `border border-gray-200` - Standard border styling
- `bg-white` - Consistent background
- `rounded-lg` - Standard border radius

### Responsive Column Spans
- `col-span-12` - Full width on mobile
- `md:col-span-3` - 3 columns on medium screens (KPI cards)
- `md:col-span-4` - 4 columns on medium screens (side panels)
- `md:col-span-6` - 6 columns on medium screens (half width)
- `md:col-span-8` - 8 columns on medium screens (main content)
- `lg:col-span-4` - 4 columns on large screens (side panels)
- `lg:col-span-8` - 8 columns on large screens (main content)

## Implementation Notes

1. All duplicate PageHeader components were removed from individual analytics components to prevent title duplication
2. All cards now use consistent styling with shadow, border, background, and rounded corners
3. Grid layouts use the same 12-column system with gap-6 spacing
4. Responsive behavior is consistent across all pages
5. Vertical spacing between sections uses space-y-6 consistently