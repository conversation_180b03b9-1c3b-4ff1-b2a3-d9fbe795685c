'use client'

import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { ChevronLeft, ChevronRight } from 'lucide-react'

interface NotificationPaginationProps {
  currentPage: number
  totalPages: number
  indexOfFirstNotification: number
  indexOfLastNotification: number
  totalFilteredNotifications: number
  goToPrevPage: () => void
  goToNextPage: () => void
  setCurrentPage: (page: number) => void
}

export function NotificationPagination({
  currentPage,
  totalPages,
  indexOfFirstNotification,
  indexOfLastNotification,
  totalFilteredNotifications,
  goToPrevPage,
  goToNextPage,
  setCurrentPage
}: NotificationPaginationProps) {
  // Always show pagination when there are notifications, even if only one page
  if (totalFilteredNotifications <= 0) return null

  return (
    <Card className="border border-gray-200 bg-white">
      <CardContent className="p-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div className="text-xs text-gray-700">
            Showing <span className="font-medium">{indexOfFirstNotification + 1}</span> to{' '}
            <span className="font-medium">
              {Math.min(indexOfLastNotification, totalFilteredNotifications)}
            </span>{' '}
            of <span className="font-medium">{totalFilteredNotifications}</span> notifications
          </div>

          {totalPages > 1 && (
            <div className="flex items-center gap-1.5">
              <Button
                variant="outline"
                size="sm"
                onClick={goToPrevPage}
                disabled={currentPage === 1}
                className="flex items-center gap-1.5 h-7 px-2.5 text-xs"
              >
                <ChevronLeft className="h-3.5 w-3.5" />
                Previous
              </Button>

              <div className="flex items-center gap-1">
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let page
                  if (totalPages <= 5) {
                    page = i + 1
                  } else if (currentPage <= 3) {
                    page = i + 1
                  } else if (currentPage >= totalPages - 2) {
                    page = totalPages - 4 + i
                  } else {
                    page = currentPage - 2 + i
                  }

                  return (
                    <Button
                      key={page}
                      variant={currentPage === page ? "default" : "outline"}
                      size="sm"
                      className="w-7 h-7 p-0 text-xs"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  )
                })}
              </div>

              <Button
                variant="outline"
                size="sm"
                onClick={goToNextPage}
                disabled={currentPage === totalPages}
                className="flex items-center gap-1.5 h-7 px-2.5 text-xs"
              >
                Next
                <ChevronRight className="h-3.5 w-3.5" />
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}