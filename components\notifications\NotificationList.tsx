'use client'

import { NotificationItem } from './NotificationItem'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications?: Notification[]
}

interface NotificationListProps {
  notifications: Notification[]
  expandedNotifications: string[]
  toggleNotificationExpansion: (id: string) => void
  formatDate: (dateString: string) => string
  getPanelNotificationIconProps: (type: string) => {
    Icon: any
    bgClass: string
    iconClass: string
  }
  getPriorityBadge: (priority?: string) => JSX.Element | null
  formatFullDate: (dateString: string) => string
}

export function NotificationList({
  notifications,
  expandedNotifications,
  toggleNotificationExpansion,
  formatDate,
  getPanelNotificationIconProps,
  getPriorityBadge,
  formatFullDate
}: NotificationListProps) {
  return (
    <div className="space-y-2">
      {notifications.map((notification) => (
        <NotificationItem
          key={notification.id}
          notification={notification}
          isExpanded={expandedNotifications.includes(notification.id)}
          toggleExpansion={toggleNotificationExpansion}
          formatDate={formatDate}
          getPanelNotificationIconProps={getPanelNotificationIconProps}
          getPriorityBadge={getPriorityBadge}
          formatFullDate={formatFullDate}
        />
      ))}
    </div>
  )
}