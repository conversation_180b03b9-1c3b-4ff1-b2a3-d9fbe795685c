'use client'

import React, { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Wand2, Package, AlertTriangle, CheckCircle } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getButtonClasses, getBadgeClasses } from '../../../config/design-system'

interface VariantCombination {
  id: string
  attributes: { [key: string]: string }
  sku: string
  enabled: boolean
}

export function VariantGenerator() {
  const { formData, savedVariants, setSavedVariants } = useProductForm()
  const [generatedVariants, setGeneratedVariants] = useState<VariantCombination[]>([])
  const [isGenerating, setIsGenerating] = useState(false)

  // Generate all possible combinations
  const generateVariantCombinations = () => {
    const attributes = formData.variant_attributes.filter(attr => attr.values.length > 0)
    
    if (attributes.length === 0) {
      return []
    }

    // Generate cartesian product of all attribute values
    const combinations: Array<{ [key: string]: string }> = []
    
    const generateCombos = (index: number, current: { [key: string]: string }) => {
      if (index === attributes.length) {
        combinations.push({ ...current })
        return
      }
      
      const attribute = attributes[index]
      for (const value of attribute.values) {
        current[attribute.name] = value
        generateCombos(index + 1, current)
      }
    }
    
    generateCombos(0, {})
    
    // Convert to variant combinations with SKUs
    return combinations.map((combo, index) => {
      const variantSku = generateVariantSku(combo)
      return {
        id: `variant_${Date.now()}_${index}`,
        attributes: combo,
        sku: variantSku,
        enabled: true
      }
    })
  }

  // Generate SKU for variant based on attributes
  const generateVariantSku = (attributes: { [key: string]: string }) => {
    const baseSku = formData.base_sku || 'PROD'
    const attributeParts = Object.values(attributes)
      .map(value => value.substring(0, 3).toUpperCase())
      .join('-')
    
    return `${baseSku}-${attributeParts}`
  }

  // Handle variant generation
  const handleGenerateVariants = () => {
    setIsGenerating(true)
    
    // Simulate generation delay for UX
    setTimeout(() => {
      const variants = generateVariantCombinations()
      setGeneratedVariants(variants)
      setIsGenerating(false)
    }, 500)
  }

  // Toggle variant enabled state
  const toggleVariantEnabled = (variantId: string) => {
    setGeneratedVariants(prev => prev.map(variant =>
      variant.id === variantId
        ? { ...variant, enabled: !variant.enabled }
        : variant
    ))
  }

  // Apply generated variants to form
  const applyGeneratedVariants = () => {
    const enabledVariants = generatedVariants.filter(v => v.enabled)
    
    // Convert to ProductVariant format
    const productVariants = enabledVariants.map(variant => ({
      id: variant.id,
      attributes: Object.entries(variant.attributes).map(([name, value]) => ({ name, value })),
      price: null,
      base_cost: null,
      packaging_cost: null,
      quantity: 0,
      sku: variant.sku,
      low_stock_threshold: 5
    }))
    
    setSavedVariants(productVariants)
  }

  // Auto-generate when attributes change
  useEffect(() => {
    if (formData.variant_attributes.some(attr => attr.values.length > 0)) {
      handleGenerateVariants()
    } else {
      setGeneratedVariants([])
    }
  }, [formData.variant_attributes])

  const canGenerate = formData.variant_attributes.some(attr => attr.values.length > 0)
  const totalCombinations = formData.variant_attributes.reduce((total, attr) => 
    total * Math.max(attr.values.length, 1), 1
  )
  const enabledCount = generatedVariants.filter(v => v.enabled).length

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Variant Generation
        </h3>
        <p className={designSystem.typography.caption}>
          Generate all possible product variations based on your attributes
        </p>
      </div>

      {/* Generation Status */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className={designSystem.typography.subsectionTitle}>
              Variant Combinations
            </CardTitle>
            <div className="flex items-center gap-2">
              {canGenerate && (
                <Badge className={getBadgeClasses('primary')}>
                  {totalCombinations} possible
                </Badge>
              )}
              <Button
                type="button"
                onClick={handleGenerateVariants}
                disabled={!canGenerate || isGenerating}
                className={getButtonClasses('primary')}
              >
                {isGenerating ? (
                  <div className="w-4 h-4 border border-white border-t-transparent rounded-full animate-spin mr-2" />
                ) : (
                  <Wand2 className="w-4 h-4 mr-2" />
                )}
                {isGenerating ? 'Generating...' : 'Generate Variants'}
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="pt-0">
          {!canGenerate ? (
            <div className="text-center py-8">
              <AlertTriangle className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className={designSystem.typography.body}>
                Add attributes with values to generate variants
              </p>
              <p className={designSystem.typography.caption}>
                Each attribute needs at least one value
              </p>
            </div>
          ) : generatedVariants.length === 0 ? (
            <div className="text-center py-8">
              <Package className="w-12 h-12 mx-auto mb-4 text-gray-400" />
              <p className={designSystem.typography.body}>
                Click "Generate Variants" to create combinations
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Summary */}
              <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-md">
                <div className="flex items-center gap-2">
                  <CheckCircle className="w-5 h-5 text-blue-600" />
                  <span className={`${designSystem.typography.label} text-blue-700`}>
                    {enabledCount} of {generatedVariants.length} variants will be created
                  </span>
                </div>
                <Button
                  type="button"
                  onClick={applyGeneratedVariants}
                  disabled={enabledCount === 0}
                  className={getButtonClasses('primary')}
                >
                  Apply Variants
                </Button>
              </div>

              {/* Variant List */}
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {generatedVariants.map((variant) => (
                  <div
                    key={variant.id}
                    className={`
                      flex items-center justify-between p-3 border rounded-md cursor-pointer transition-colors
                      ${variant.enabled 
                        ? 'border-blue-200 bg-blue-50' 
                        : 'border-gray-200 bg-gray-50'
                      }
                    `}
                    onClick={() => toggleVariantEnabled(variant.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`
                        w-4 h-4 rounded border-2 flex items-center justify-center
                        ${variant.enabled 
                          ? 'border-blue-500 bg-blue-500' 
                          : 'border-gray-300'
                        }
                      `}>
                        {variant.enabled && (
                          <CheckCircle className="w-3 h-3 text-white" />
                        )}
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2">
                          {Object.entries(variant.attributes).map(([name, value]) => (
                            <Badge key={name} className={getBadgeClasses('secondary')}>
                              {name}: {value}
                            </Badge>
                          ))}
                        </div>
                        <p className={`${designSystem.typography.caption} mt-1`}>
                          SKU: {variant.sku}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Bulk Actions */}
              <div className="flex gap-2 pt-2 border-t border-gray-200">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setGeneratedVariants(prev => prev.map(v => ({ ...v, enabled: true })))}
                  className="text-xs"
                >
                  Enable All
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setGeneratedVariants(prev => prev.map(v => ({ ...v, enabled: false })))}
                  className="text-xs"
                >
                  Disable All
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
