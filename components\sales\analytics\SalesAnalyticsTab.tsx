'use client'

import { useState, useMemo } from 'react'
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { MetricCard } from '@/components/ui/metric-card'
import { useAllOrders } from '@/hooks/use-orders'
import { useAllCustomers } from '@/hooks/use-customers'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import { cn } from '@/lib/utils'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { 
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { Line } from 'react-chartjs-2'
import { PageHeader } from '@/components/ui/page-header'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

interface SalesAnalyticsTabProps {
  isMobile?: boolean
  className?: string
  dateRange?: '7d' | '30d' | '90d' | '1y'
}

export function SalesAnalyticsTab({ 
  className,
  dateRange = '30d'
}: SalesAnalyticsTabProps) {
  const [topProductsCount, setTopProductsCount] = useState<number>(5)
  const [topCustomersCount, setTopCustomersCount] = useState<number>(5)
  
  const { user, organizationId } = useAuth()
  const { formatCurrency } = useCurrency()
  
  // Use React Query to fetch orders and customers
  const { data: orders = [], isLoading: isLoadingOrders } = useAllOrders(organizationId || undefined)
  const { data: customers = [], isLoading: isLoadingCustomers } = useAllCustomers(organizationId || undefined)

  // Process analytics data
  const analyticsData = useMemo(() => {
    // Filter orders based on date range
    const now = new Date()
    const startDate = new Date()
    
    switch (dateRange) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
    }
    
    const filteredOrders = (orders as any[]).filter((order: any) => 
      new Date(order.order_date) >= startDate
    )
    
    // Calculate KPIs
    const totalRevenue = filteredOrders.reduce((sum: number, order: any) => sum + (order.total || 0), 0)
    const totalOrders = filteredOrders.length
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0
    
    // Calculate new customers (customers who made their first order in the date range)
    const customerOrderMap = new Map<string, Date[]>()
    filteredOrders.forEach((order: any) => {
      if (order.customer_id) {
        if (!customerOrderMap.has(order.customer_id)) {
          customerOrderMap.set(order.customer_id, [])
        }
        customerOrderMap.get(order.customer_id)!.push(new Date(order.order_date))
      }
    })
    
    // Find customers whose first order was in this date range
    let newCustomers = 0
    customerOrderMap.forEach(dates => {
      const earliestOrder = new Date(Math.min(...dates.map(date => date.getTime())))
      if (earliestOrder >= startDate) {
        newCustomers++
      }
    })
    
    // Calculate sales over time for chart
    const salesOverTime: number[] = []
    const labels: Date[] = []
    
    // Generate date labels and initialize sales data
    const currentDate = new Date(startDate)
    while (currentDate <= now) {
      labels.push(new Date(currentDate))
      salesOverTime.push(0)
      currentDate.setDate(currentDate.getDate() + 1)
    }
    
    // Aggregate sales by day
    filteredOrders.forEach((order: any) => {
      const orderDate = new Date(order.order_date)
      const dayIndex = Math.floor((orderDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      if (dayIndex >= 0 && dayIndex < salesOverTime.length) {
        salesOverTime[dayIndex] += order.total || 0
      }
    })
    
    // Calculate top selling products (simplified - in a real implementation, we would join with order_items)
    // For now, we'll group by order to show variety
    const productSalesMap = new Map<string, { revenue: number, count: number }>()
    filteredOrders.forEach((order: any) => {
      const productName = `Order #${order.order_number}`
      if (productSalesMap.has(productName)) {
        const current = productSalesMap.get(productName)!
        productSalesMap.set(productName, {
          revenue: current.revenue + (order.total || 0),
          count: current.count + 1
        })
      } else {
        productSalesMap.set(productName, {
          revenue: order.total || 0,
          count: 1
        })
      }
    })
    
    const topSellingProducts = Array.from(productSalesMap.entries())
      .map(([name, data]) => ({
        name,
        revenue: data.revenue,
        orders: data.count
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, topProductsCount)
    
    // Calculate sales by customer
    const customerSalesMap = new Map<string, { revenue: number, orders: number, name: string }>()
    filteredOrders.forEach((order: any) => {
      const customerId = order.customer_id
      // Access the customer_name property that's added by getAllOrdersForOrganization
      const customerName = (order as any).customer_name || 'Unknown Customer'
      
      if (customerId) {
        if (customerSalesMap.has(customerId)) {
          const current = customerSalesMap.get(customerId)!
          customerSalesMap.set(customerId, {
            revenue: current.revenue + (order.total || 0),
            orders: current.orders + 1,
            name: customerName
          })
        } else {
          customerSalesMap.set(customerId, {
            revenue: order.total || 0,
            orders: 1,
            name: customerName
          })
        }
      }
    })
    
    const salesByCustomer = Array.from(customerSalesMap.entries())
      .map(([id, data]) => ({
        id,
        name: data.name,
        revenue: data.revenue,
        orders: data.orders
      }))
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, topCustomersCount)
    
    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      newCustomers,
      salesOverTime,
      labels,
      topSellingProducts,
      salesByCustomer
    }
  }, [orders, dateRange, topProductsCount, topCustomersCount])

  // Generate chart data
  const chartData = useMemo(() => {
    return {
      labels: analyticsData.labels.map(date => 
        date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
      ),
      datasets: [
        {
          label: 'Revenue',
          data: analyticsData.salesOverTime,
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4,
        }
      ]
    }
  }, [analyticsData])

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        }
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(255, 255, 255, 0.9)',
        titleColor: '#1e293b',
        bodyColor: '#1e293b',
        borderColor: '#e2e8f0',
        borderWidth: 1,
        padding: 12,
        displayColors: true,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false,
        },
        ticks: {
          color: '#94a3b8',
        }
      },
      y: {
        grid: {
          color: '#f1f5f9',
        },
        ticks: {
          color: '#94a3b8',
          callback: function(value: any) {
            return formatCurrency(value)
          }
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    }
  }

  if (isLoadingOrders || isLoadingCustomers) {
    return (
      <div className={cn("space-y-6", className)}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <h3 className="text-lg font-medium">Loading sales analytics...</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Please wait while we fetch your sales data.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Main Grid Layout - 12 columns */}
      <div className="grid grid-cols-12 gap-6">
        {/* KPI Cards - Top Row */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Total Revenue"
            value={formatCurrency(analyticsData.totalRevenue)}
            subtitle="In selected period"
            icon={
              <div className="h-6 w-6 rounded-md bg-blue-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              </div>
            }
          />
        </div>
        
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Total Orders"
            value={analyticsData.totalOrders}
            subtitle="Completed sales"
            icon={
              <div className="h-6 w-6 rounded-md bg-green-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-green-500"></div>
              </div>
            }
          />
        </div>
        
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="Average Order Value"
            value={formatCurrency(analyticsData.averageOrderValue)}
            subtitle="Per transaction"
            icon={
              <div className="h-6 w-6 rounded-md bg-yellow-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              </div>
            }
          />
        </div>
        
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="New Customers"
            value={analyticsData.newCustomers}
            subtitle="First-time buyers"
            icon={
              <div className="h-6 w-6 rounded-md bg-purple-50 flex items-center justify-center">
                <div className="w-3 h-3 rounded-full bg-purple-500"></div>
              </div>
            }
          />
        </div>

        {/* Sales Over Time Chart - Full Width */}
        <div className="col-span-12">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
            <CardHeader>
              <CardTitle>Sales Over Time</CardTitle>
              <CardDescription>
                Revenue trends in selected period
              </CardDescription>
            </CardHeader>
            <CardContent className="h-80">
              <Line data={chartData} options={chartOptions} />
            </CardContent>
          </Card>
        </div>

        {/* Bottom Row */}
        {/* Top Selling Products - 8 columns */}
        <div className="col-span-12 md:col-span-8">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-lg">Top Selling Products</CardTitle>
                <CardDescription className="text-xs">
                  Highest revenue generating items
                </CardDescription>
              </div>
              <Select 
                value={topProductsCount.toString()} 
                onValueChange={(value) => setTopProductsCount(parseInt(value))}
              >
                <SelectTrigger className="w-[100px] h-7 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5" className="text-xs">5 Items</SelectItem>
                  <SelectItem value="10" className="text-xs">10 Items</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-slate-100">
                      <th className="text-left py-2 px-3 font-medium text-xs">Product</th>
                      <th className="text-right py-2 px-3 font-medium text-xs">Orders</th>
                      <th className="text-right py-2 px-3 font-medium text-xs">Revenue</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.topSellingProducts.map((product, index) => (
                      <tr key={index} className="border-b border-slate-100 last:border-0">
                        <td className="py-2 px-3">{product.name}</td>
                        <td className="py-2 px-3 text-right text-xs">{product.orders}</td>
                        <td className="py-2 px-3 text-right font-medium">{formatCurrency(product.revenue)}</td>
                      </tr>
                    ))}
                    {analyticsData.topSellingProducts.length === 0 && (
                      <tr>
                        <td colSpan={3} className="py-4 px-3 text-center text-muted-foreground text-xs">
                          No sales data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sales by Customer - 4 columns */}
        <div className="col-span-12 md:col-span-4">
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg h-full">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <div>
                <CardTitle className="text-lg">Top Customers</CardTitle>
                <CardDescription className="text-xs">
                  Highest spending customers
                </CardDescription>
              </div>
              <Select 
                value={topCustomersCount.toString()} 
                onValueChange={(value) => setTopCustomersCount(parseInt(value))}
              >
                <SelectTrigger className="w-[100px] h-7 text-xs">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5" className="text-xs">5 Customers</SelectItem>
                  <SelectItem value="10" className="text-xs">10 Customers</SelectItem>
                </SelectContent>
              </Select>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead>
                    <tr className="bg-slate-100">
                      <th className="text-left py-2 px-3 font-medium text-xs">Customer</th>
                      <th className="text-right py-2 px-3 font-medium text-xs">Orders</th>
                      <th className="text-right py-2 px-3 font-medium text-xs">Spent</th>
                    </tr>
                  </thead>
                  <tbody>
                    {analyticsData.salesByCustomer.map((customer, index) => (
                      <tr key={index} className="border-b border-slate-100 last:border-0">
                        <td className="py-2 px-3 truncate max-w-[120px]">{customer.name}</td>
                        <td className="py-2 px-3 text-right text-xs">{customer.orders}</td>
                        <td className="py-2 px-3 text-right font-medium">{formatCurrency(customer.revenue)}</td>
                      </tr>
                    ))}
                    {analyticsData.salesByCustomer.length === 0 && (
                      <tr>
                        <td colSpan={3} className="py-4 px-3 text-center text-muted-foreground text-xs">
                          No customer data available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}