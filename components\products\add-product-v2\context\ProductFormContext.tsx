'use client'

import React, { createContext, useContext, useState, useCallback, useEffect } from 'react'
import { ProductFormData, FormErrors, FormValidation, INITIAL_FORM_DATA, ProductVariant } from '../types'

interface ProductFormContextType {
  // Form data
  formData: ProductFormData
  updateFormData: (field: keyof ProductFormData, value: any) => void
  resetForm: () => void
  
  // Navigation
  currentSection: string
  setCurrentSection: (section: string) => void
  
  // Validation
  errors: FormErrors
  setErrors: React.Dispatch<React.SetStateAction<FormErrors>>
  isValid: FormValidation
  
  // Variants (for variable products)
  currentVariant: {
    attributes: Array<{ name: string; value: string }>
    price: number | null
    base_cost: number | null
    packaging_cost: number | null
    quantity: number
    sku: string
    low_stock_threshold: number
  }
  setCurrentVariant: React.Dispatch<React.SetStateAction<any>>
  savedVariants: ProductVariant[]
  setSavedVariants: React.Dispatch<React.SetStateAction<ProductVariant[]>>
  
  // Submission
  isSubmitting: boolean
  setIsSubmitting: React.Dispatch<React.SetStateAction<boolean>>
  
  // Categories and suppliers
  categories: Array<{ id: string; name: string }>
  setCategories: React.Dispatch<React.SetStateAction<Array<{ id: string; name: string }>>>
  suppliers: Array<{ id: string; name: string }>
  setSuppliers: React.Dispatch<React.SetStateAction<Array<{ id: string; name: string }>>>
}

const ProductFormContext = createContext<ProductFormContextType | undefined>(undefined)

export function ProductFormProvider({ children }: { children: React.ReactNode }) {
  const [formData, setFormData] = useState<ProductFormData>(INITIAL_FORM_DATA)
  const [currentSection, setCurrentSection] = useState('basics')
  const [errors, setErrors] = useState<FormErrors>({})
  const [isValid, setIsValid] = useState<FormValidation>({})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [categories, setCategories] = useState<Array<{ id: string; name: string }>>([])
  const [suppliers, setSuppliers] = useState<Array<{ id: string; name: string }>>([])
  
  // Variant state
  const [currentVariant, setCurrentVariant] = useState({
    attributes: [],
    price: null,
    base_cost: null,
    packaging_cost: null,
    quantity: 0,
    sku: '',
    low_stock_threshold: 10
  })
  const [savedVariants, setSavedVariants] = useState<ProductVariant[]>([])

  const updateFormData = useCallback((field: keyof ProductFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }))
    }
  }, [errors])

  const resetForm = useCallback(() => {
    setFormData(INITIAL_FORM_DATA)
    setCurrentSection('basics')
    setErrors({})
    setIsValid({})
    setSavedVariants([])
    setCurrentVariant({
      attributes: [],
      price: null,
      base_cost: null,
      packaging_cost: null,
      quantity: 0,
      sku: '',
      low_stock_threshold: 10
    })
  }, [])

  // Validation logic
  useEffect(() => {
    const validateSection = (sectionId: string): boolean => {
      switch (sectionId) {
        case 'basics':
          return !!(formData.name && formData.base_sku)
        case 'pricing':
          if (formData.has_variants) {
            return formData.variant_attributes.length > 0 && savedVariants.length > 0
          } else {
            return !!formData.price
          }
        case 'inventory':
          // Inventory section is valid when stock quantity is explicitly set by user
          return formData.stock_quantity !== undefined && formData.stock_quantity !== null && formData.stock_quantity >= 0
        default:
          return false
      }
    }

    const newIsValid: FormValidation = {}
    const sections = ['basics', 'pricing', 'inventory']

    sections.forEach(sectionId => {
      newIsValid[sectionId] = validateSection(sectionId)
    })

    setIsValid(newIsValid)
  }, [formData, savedVariants])

  const value: ProductFormContextType = {
    formData,
    updateFormData,
    resetForm,
    currentSection,
    setCurrentSection,
    errors,
    setErrors,
    isValid,
    currentVariant,
    setCurrentVariant,
    savedVariants,
    setSavedVariants,
    isSubmitting,
    setIsSubmitting,
    categories,
    setCategories,
    suppliers,
    setSuppliers
  }

  return (
    <ProductFormContext.Provider value={value}>
      {children}
    </ProductFormContext.Provider>
  )
}

export function useProductForm() {
  const context = useContext(ProductFormContext)
  if (context === undefined) {
    throw new Error('useProductForm must be used within a ProductFormProvider')
  }
  return context
}