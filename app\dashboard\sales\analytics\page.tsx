'use client'

import { useState } from 'react'
import { SalesAnalyticsTab } from '@/components/sales/analytics/SalesAnalyticsTab'
import { PageHeader } from '@/components/ui/page-header'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'

export default function SalesAnalyticsPage() {
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  return (
    <div className="space-y-6">
      <SalesAnalyticsTab dateRange={dateRange} />
    </div>
  )
}