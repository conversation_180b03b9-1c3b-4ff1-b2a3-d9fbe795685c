'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Package, Layers, CheckCircle } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getCardClasses } from '../../../config/design-system'

interface ProductTypeOption {
  id: 'simple' | 'variable'
  title: string
  description: string
  icon: React.ComponentType<any>
}

const PRODUCT_TYPES: ProductTypeOption[] = [
  {
    id: 'simple',
    title: 'Simple Product',
    description: 'A single product with no variations',
    icon: Package
  },
  {
    id: 'variable',
    title: 'Variable Product',
    description: 'Product with multiple variations',
    icon: Layers
  }
]

export function ProductTypeSelector() {
  const { formData, updateFormData } = useProductForm()

  const handleTypeSelect = (type: 'simple' | 'variable') => {
    updateFormData('has_variants', type === 'variable')
  }

  const selectedType = formData.has_variants ? 'variable' : 'simple'

  return (
    <div className="space-y-4">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Choose Product Type
        </h3>
        <p className={designSystem.typography.caption}>
          Select the type that best describes your product
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {PRODUCT_TYPES.map((type) => {
          const Icon = type.icon
          const isSelected = selectedType === type.id
          
          return (
            <Card
              key={type.id}
              className={`
                ${getCardClasses()}
                cursor-pointer transition-all duration-200
                ${isSelected 
                  ? 'ring-2 ring-gray-900 border-gray-900 bg-gray-50' 
                  : 'hover:border-gray-300 hover:shadow-md'
                }
              `}
              onClick={() => handleTypeSelect(type.id)}
            >
              <CardContent className="p-4">
                <div className="flex items-start gap-3">
                  {/* Icon and selection indicator */}
                  <div className="flex-shrink-0">
                    <div className={`
                      w-10 h-10 rounded-lg flex items-center justify-center border border-gray-200
                      ${isSelected 
                        ? 'bg-gray-100 border-gray-900' 
                        : 'bg-white'
                      }
                    `}>
                      <Icon className={`w-5 h-5 ${isSelected ? 'text-gray-900' : 'text-gray-800'}`} />
                    </div>
                  </div>

                  {/* Content */}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className={`${designSystem.typography.label} ${isSelected ? 'text-gray-900' : ''}`}>
                        {type.title}
                      </h4>
                      {isSelected && (
                        <CheckCircle className="w-4 h-4 text-gray-900" />
                      )}
                    </div>
                    
                    <p className={`${designSystem.typography.caption}`}>
                      {type.description}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}