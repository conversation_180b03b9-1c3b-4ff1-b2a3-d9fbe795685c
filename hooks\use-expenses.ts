import { useQuery, useQueryClient, useMutation } from '@tanstack/react-query'
import { getAllExpensesForOrganization } from '@/lib/supabase'
import type { ExpenseRow } from '@/lib/supabase'

// Debounce function to prevent rapid requests
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout | null = null
  return function (...args: Parameters<T>) {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}

/**
 * Custom hook to fetch all expenses for an organization with React Query
 * @param organizationId - The organization ID to fetch expenses for
 * @returns React Query result object with expenses data
 */
export function useAllExpenses(organizationId: string | undefined) {
  return useQuery<ExpenseRow[], Error>({
    queryKey: ['allExpenses', organizationId],
    queryFn: async () => {
      if (!organizationId) {
        throw new Error('Organization ID is required')
      }
      
      const data = await getAllExpensesForOrganization(organizationId)
      return data || []
    },
    enabled: !!organizationId,
    staleTime: 2 * 60 * 1000, // 2 minutes - consistent with providers
    gcTime: 5 * 60 * 1000, // 5 minutes garbage collection time
    refetchOnMount: true,
    refetchOnWindowFocus: true, // Important for tab switching
    refetchOnReconnect: true,
    refetchInterval: false,
    refetchIntervalInBackground: false,
    retry: 3, // Increased retry count
    retryDelay: (attemptIndex) => {
      return Math.min(1000 * 2 ** attemptIndex, 30000)
    },
    networkMode: 'online' // Handle offline scenarios
  })
}

/**
 * Custom hook to invalidate expense queries and trigger refetching with debouncing
 * @returns Object with functions to invalidate queries
 */
export function useInvalidateExpenses() {
  const queryClient = useQueryClient()
  
  // Debounced invalidate function
  const debouncedInvalidateAllExpenses = debounce(() => {
    queryClient.invalidateQueries({ queryKey: ['allExpenses'] })
  }, 1000) // 1 second debounce
  
  return {
    invalidateAllExpenses: debouncedInvalidateAllExpenses
  }
}