'use client'

import React, { useState, useMemo, useCallback, useEffect } from 'react'
import { InvoiceTableHeader, INITIAL_INVOICE_FILTERS, type InvoiceFilters } from './InvoiceTableHeader'
import { EnhancedInvoiceTable } from './EnhancedInvoiceTable'
import { useAllInvoices, useUpdateInvoiceStatus } from '@/hooks/use-invoices'
import { useAuth } from '@/contexts/auth-context'
import { getSupabaseClient } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'

interface InvoicesTabProps {
  isMobile?: boolean
  className?: string
}

export default function InvoicesTab({
  isMobile = false,
  className
}: InvoicesTabProps) {
  // State for filters
  const [filters, setFilters] = useState<InvoiceFilters>(INITIAL_INVOICE_FILTERS)
  
  const { organizationId } = useAuth()
  const { toast } = useToast()
  const { mutate: updateInvoiceStatus } = useUpdateInvoiceStatus()
  
  // Use React Query to fetch invoices
  const { data: invoices = [], isLoading, error, refetch } = useAllInvoices(organizationId || undefined)
  
  // Memoize the Supabase client instance to prevent re-creation on every render
  const supabase = useMemo(() => getSupabaseClient(), [])
  
  // Filter invoices based on current filters
  const filteredInvoices = useMemo(() => {
    let result = [...invoices]
    
    // Text search
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase()
      result = result.filter(invoice => 
        invoice.invoice_number?.toLowerCase().includes(query) ||
        invoice.customer_name?.toLowerCase().includes(query)
      )
    }
    
    // Status filter
    if (filters.status.length > 0) {
      result = result.filter(invoice => 
        filters.status.includes(invoice.status)
      )
    }
    
    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      result = result.filter(invoice => {
        const issueDate = new Date(invoice.issue_date)
        
        if (filters.dateRange.from && issueDate < filters.dateRange.from) {
          return false
        }
        
        if (filters.dateRange.to && issueDate > filters.dateRange.to) {
          return false
        }
        
        return true
      })
    }
    
    return result
  }, [invoices, filters])
  
  // Handle filters change
  const handleFiltersChange = useCallback((newFilters: InvoiceFilters) => {
    setFilters(newFilters)
  }, [])
  
  // Handle invoice view
  const handleInvoiceView = useCallback((invoice: any) => {
    console.log('View invoice:', invoice)
    // In a real implementation, this would open a modal or navigate to invoice details
  }, [])
  
  // Handle invoice edit
  const handleInvoiceEdit = useCallback((invoice: any) => {
    console.log('Edit invoice:', invoice)
    // In a real implementation, this would open an edit modal
  }, [])
  
  // Handle invoice delete
  const handleInvoiceDelete = async (invoiceIds: string[]) => {
    if (!organizationId) return
    
    try {
      // In a real implementation, this would delete invoices from the database
      console.log('Delete invoices:', invoiceIds)
      
      // Refresh the invoices list
      refetch()
      
      // Dispatch dashboard refresh event
      window.dispatchEvent(new CustomEvent('dashboardRefresh'))
    } catch (error) {
      console.error('Error deleting invoices:', error)
    }
  }
  
  // Render loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <h3 className="text-lg font-medium">Loading invoices...</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Please wait while we fetch your invoices.
            </p>
          </div>
        </div>
      </div>
    )
  }
  
  // Render error state
  if (error) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-lg font-medium">Unable to Load Invoices</h3>
            </div>
            <p className="text-muted-foreground text-sm mb-4">
              {error instanceof Error ? error.message : 'Failed to load invoices. Please try again.'}
            </p>
            <button 
              onClick={() => refetch()} 
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className={className}>
      {/* Integrated Table Header with Filters */}
      <InvoiceTableHeader
        filters={filters}
        onFiltersChange={handleFiltersChange}
        isMobile={isMobile}
      />
      
      {/* Enhanced Invoice Table */}
      <div className="mt-4">
        <EnhancedInvoiceTable
          invoices={filteredInvoices}
          onInvoiceView={handleInvoiceView}
          onInvoiceEdit={handleInvoiceEdit}
          onInvoiceDelete={handleInvoiceDelete}
          showBulkActions={true}
          defaultPageSize={isMobile ? 5 : 10}
          isMobile={isMobile}
        />
      </div>
    </div>
  )
}