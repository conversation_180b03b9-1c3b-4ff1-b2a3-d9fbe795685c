'use client'

import { Checkbox } from '@/components/ui/checkbox'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Archive, Trash2, X } from 'lucide-react'

interface BulkActionsProps {
  selectedNotifications: string[]
  currentNotifications: any[]
  toggleSelectAll: () => void
  archiveSelected: () => void
  deleteSelected: () => void
  clearSelection: () => void
}

export function BulkActions({
  selectedNotifications,
  currentNotifications,
  toggleSelectAll,
  archiveSelected,
  deleteSelected,
  clearSelection
}: BulkActionsProps) {
  if (selectedNotifications.length === 0) return null

  return (
    <Card className="border-blue-200 bg-blue-50/50">
      <CardContent className="p-3">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
          <div className="flex items-center gap-2">
            <Checkbox
              checked={selectedNotifications.length === currentNotifications.length && currentNotifications.length > 0}
              onCheckedChange={toggleSelectAll}
              className="h-4 w-4"
            />
            <div className="flex items-center gap-1.5">
              <Badge variant="secondary" className="bg-blue-100 text-blue-800 text-xs px-1.5 py-0.5">
                {selectedNotifications.length}
              </Badge>
              <span className="text-xs font-medium text-blue-900">
                notification{selectedNotifications.length > 1 ? 's' : ''} selected
              </span>
            </div>
          </div>

          <div className="flex items-center gap-1.5">
            <Button
              variant="outline"
              size="sm"
              onClick={archiveSelected}
              className="border-blue-300 text-blue-700 hover:bg-blue-100 h-7 px-2.5 text-xs"
            >
              <Archive className="h-3 w-3 mr-1.5" />
              Archive
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={deleteSelected}
              className="border-red-300 text-red-700 hover:bg-red-100 h-7 px-2.5 text-xs"
            >
              <Trash2 className="h-3 w-3 mr-1.5" />
              Delete
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={clearSelection}
              className="text-gray-600 h-7 px-2.5 text-xs"
            >
              <X className="h-3 w-3 mr-1.5" />
              Clear
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}