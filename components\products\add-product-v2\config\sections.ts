import {
  Package,
  Info,
  DollarSign,
  Settings
} from 'lucide-react'
import { FormSection } from '../types'

// New 3-section architecture for better UX
export const FORM_SECTIONS: FormSection[] = [
  {
    id: 'basics',
    label: 'Product Basics',
    icon: Package,
    required: true,
    description: 'Product type, details, and categorization'
  },
  {
    id: 'pricing',
    label: 'Pricing & Variants',
    icon: DollarSign,
    required: true,
    description: 'Unified pricing and variant management'
  },
  {
    id: 'inventory',
    label: 'Inventory & Settings',
    icon: Settings,
    required: false,
    description: 'Stock management and additional settings'
  }
]

export function getSectionById(id: string): FormSection | undefined {
  return FORM_SECTIONS.find(section => section.id === id)
}

export function getNextSection(currentId: string): string | null {
  const currentIndex = FORM_SECTIONS.findIndex(section => section.id === currentId)
  if (currentIndex === -1 || currentIndex === FORM_SECTIONS.length - 1) {
    return null
  }
  return FORM_SECTIONS[currentIndex + 1].id
}

export function getPreviousSection(currentId: string): string | null {
  const currentIndex = FORM_SECTIONS.findIndex(section => section.id === currentId)
  if (currentIndex <= 0) {
    return null
  }
  return FORM_SECTIONS[currentIndex - 1].id
}
