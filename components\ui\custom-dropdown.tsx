"use client"

import * as React from "react"
import { Check, ChevronDown, Plus, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button, type ButtonProps } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

// Custom styles to override any disabled states
const customDropdownStyles = `
  .custom-dropdown-item {
    opacity: 1 !important;
    pointer-events: auto !important;
    color: inherit !important;
    cursor: pointer !important;
  }
  .custom-dropdown-item:hover {
    background-color: hsl(var(--accent)) !important;
    color: hsl(var(--accent-foreground)) !important;
  }
  .custom-dropdown-item[data-disabled="true"] {
    opacity: 1 !important;
    pointer-events: auto !important;
  }
`

export interface CustomDropdownOption {
  value: string
  label: string
  isCustom?: boolean
  isDivider?: boolean
  // Add a unique key property to ensure React keys are unique
  key?: string
}

interface CustomDropdownProps {
  options: CustomDropdownOption[]
  value?: string
  onValueChange: (value: string) => void
  onAddCustomOption?: (option: CustomDropdownOption) => void
  placeholder?: string
  emptyText?: string
  allowCustom?: boolean
  customPlaceholder?: string
  className?: string
  disabled?: boolean
}

export function CustomDropdown({
  options,
  value,
  onValueChange,
  onAddCustomOption,
  placeholder = "Select option...",
  emptyText = "No options found.",
  allowCustom = true,
  customPlaceholder = "Add custom option...",
  className,
  disabled = false,
}: CustomDropdownProps) {
  const [open, setOpen] = React.useState(false)
  const [showCustomInput, setShowCustomInput] = React.useState(false)
  const [customValue, setCustomValue] = React.useState("")

  const selectedOption = options.find((option) => option.value === value)

  const handleAddCustom = () => {
    if (customValue.trim() && onAddCustomOption) {
      const newOption: CustomDropdownOption = {
        value: customValue.trim(),
        label: customValue.trim(),
        isCustom: true
      }
      handleAddCustomOption(newOption)
      setCustomValue("")
      setShowCustomInput(false)
    }
  }

  const handleCancelCustom = () => {
    setCustomValue("")
    setShowCustomInput(false)
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      handleAddCustom()
    } else if (e.key === "Escape") {
      handleCancelCustom()
    }
  }

  // Debug logging for selection issues
  const handleOptionSelect = (optionValue: string) => {
    console.log('CustomDropdown: Option selected:', {
      optionValue,
      currentValue: value,
      willDeselect: optionValue === value,
      options: options.map(o => ({ value: o.value, label: o.label }))
    })
    
    // Fix selection logic: Always select the option, don't deselect
    const newValue = optionValue;
    console.log('CustomDropdown: Setting new value:', newValue)
    
    onValueChange(newValue)
    setOpen(false)
  }

  // Handle custom option addition with validation
  const handleAddCustomOption = (newOption: CustomDropdownOption) => {
    console.log('CustomDropdown: Adding custom option:', newOption)
    
    // Prevent duplicate options
    const existingOption = options.find(opt => opt.value === newOption.value)
    if (existingOption) {
      console.log('CustomDropdown: Option already exists, selecting instead')
      handleOptionSelect(newOption.value)
      return
    }
    
    // Add the option if callback is provided
    if (onAddCustomOption) {
      onAddCustomOption(newOption)
    }
    
    // Select the new option
    onValueChange(newOption.value)
    setOpen(false)
  }

  // Separate regular options from divider and special options
  const regularOptions = options.filter(option => !option.isDivider && option.value !== 'create-new')
  const dividerOptions = options.filter(option => option.isDivider || option.value === 'divider')
  const specialOptions = options.filter(option => option.value === 'create-new')

  return (
    <>
      <style dangerouslySetInnerHTML={{ __html: customDropdownStyles }} />
      <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className={cn("w-full justify-between h-8 text-xs", className)}
          disabled={disabled}
        >
          {selectedOption ? <span className="text-xs">{selectedOption.label}</span> : <span className="text-xs">{placeholder}</span>}
          <ChevronDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0" align="start">
        <Command shouldFilter={false}> {/* Disable filtering to prevent items from being disabled */}
          <CommandInput placeholder="Search options..." className="placeholder:text-xs h-8 text-xs" />
          <CommandList>
            <CommandEmpty className="text-xs py-6 text-center">
              {emptyText}
            </CommandEmpty>
            <CommandGroup>
              {regularOptions.map((option, index) => (
                <CommandItem
                  key={option.key || `${option.value}-${index}`}
                  value={option.value} // Use value for consistent matching
                  disabled={false} // Explicitly ensure not disabled
                  onSelect={(selectedValue: string) => {
                    console.log('CommandItem onSelect called:', { selectedValue, optionValue: option.value })
                    handleOptionSelect(option.value)
                  }}
                  className={cn(
                    "flex items-center justify-between cursor-pointer hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[selected]:bg-accent data-[selected]:text-accent-foreground !opacity-100 !pointer-events-auto text-xs py-2",
                    "custom-dropdown-item"
                  )}
                  style={{ 
                    opacity: '1 !important', 
                    pointerEvents: 'auto' as const,
                    color: 'inherit',
                    backgroundColor: 'transparent'
                  }} // Force override disabled styling
                >
                  <div className="flex items-center">
                    <Check
                      className={cn(
                        "mr-2 h-3 w-3",
                        value === option.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <span className="text-xs">{option.label}</span>
                  </div>
                  {option.isCustom && (
                    <span className="text-xs text-muted-foreground">Custom</span>
                  )}
                </CommandItem>
              ))}
            </CommandGroup>
            
            {/* Divider and special options */}
            {dividerOptions.length > 0 && (
              <div className="border-t border-gray-200 my-1" />
            )}
            {specialOptions.map((option, index) => (
              <CommandGroup key={option.key || `${option.value}-${index}`}>
                <CommandItem
                  key={option.key || `${option.value}-${index}-item`}
                  value={option.value}
                  disabled={false}
                  onSelect={() => handleOptionSelect(option.value)}
                  className={cn(
                    "flex items-center justify-between cursor-pointer hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground data-[selected]:bg-accent data-[selected]:text-accent-foreground !opacity-100 !pointer-events-auto text-xs py-2",
                    "custom-dropdown-item"
                  )}
                  style={{ 
                    opacity: '1 !important', 
                    pointerEvents: 'auto' as const,
                    color: 'inherit',
                    backgroundColor: 'transparent'
                  }}
                >
                  <div className="flex items-center">
                    <Check
                      className={cn(
                        "mr-2 h-3 w-3",
                        value === option.value ? "opacity-100" : "opacity-0"
                      )}
                    />
                    <span className="text-xs">{option.label}</span>
                  </div>
                </CommandItem>
              </CommandGroup>
            ))}
            
            {allowCustom && (
              <CommandGroup>
              {!showCustomInput ? (
                <CommandItem
                  key="custom-option"
                  disabled={false}
                  onSelect={() => setShowCustomInput(true)}
                  className={cn(
                    "text-primary hover:text-blue-700 cursor-pointer focus:bg-accent focus:text-accent-foreground !opacity-100 !pointer-events-auto py-2 text-xs",
                    "custom-dropdown-item"
                  )}
                  style={{ 
                    opacity: '1 !important', 
                    pointerEvents: 'auto' as const,
                    color: 'rgb(37 99 235)' // blue-600
                  }}
                >
                  <Plus className="mr-2 h-3 w-3" />
                  <span className="text-xs">{customPlaceholder}</span>
                </CommandItem>
              ) : (
                <div className="p-2 border-t">
                  <div className="flex items-center space-x-2">
                    <Input
                      value={customValue}
                      onChange={(e) => setCustomValue(e.target.value)}
                      placeholder="Enter custom option"
                      className="flex-1 h-8 text-xs"
                      autoFocus
                      onKeyDown={handleKeyDown}
                    />
                    <Button
                      size="sm"
                      onClick={handleAddCustom}
                      disabled={!customValue.trim()}
                      className="h-8"
                    >
                      <Check className="h-3 w-3" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={handleCancelCustom}
                      className="h-8"
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              )}
            </CommandGroup>
          )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
    </>
  )
}