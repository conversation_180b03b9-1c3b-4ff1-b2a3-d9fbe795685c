'use client'

import { useState, useEffect } from 'react'
import { ExpenseAnalytics } from '@/components/expense-analytics'
import { usePersistedState } from '@/lib/use-persisted-state'
import { type ExpenseFilters, INITIAL_FILTERS } from '@/components/expenses/expense-table-header'
import { useAllExpenses } from '@/hooks/use-expenses'
import { useAuth } from '@/contexts/auth-context'
import { PageHeader } from '@/components/ui/page-header'

export default function ExpensesAnalyticsPage() {
  const [isMobile, setIsMobile] = useState(false)
  const { organizationId } = useAuth()
  
  // Use React Query to fetch expenses
  const { data: expenses = [], isLoading, error } = useAllExpenses(organizationId || undefined)
  
  // Shared state for filters - with persistence
  const [globalFilters, setGlobalFilters] = usePersistedState<ExpenseFilters>(
    'expenses-filters', 
    INITIAL_FILTERS
  )
  const [filteredExpenseIds, setFilteredExpenseIds] = useState<string[]>([])

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768)
    }
    
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center space-y-2">
          <div className="text-muted-foreground">Loading expense analytics...</div>
          <div className="text-xs text-muted-foreground">Fetching and processing your expense data</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-center max-w-md">
          <div className="text-red-500 mb-2">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h3 className="text-lg font-medium">Unable to Load Expense Analytics</h3>
          </div>
          <p className="text-muted-foreground text-sm mb-4">
            Failed to load expense data. Please try again.
          </p>
          <p className="text-xs text-muted-foreground mt-4">
            Error: {error instanceof Error ? error.message : String(error)}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <ExpenseAnalytics 
        expenses={expenses}
      />
    </div>
  )
}