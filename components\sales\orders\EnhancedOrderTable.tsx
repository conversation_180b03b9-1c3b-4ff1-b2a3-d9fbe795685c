'use client'

import React, { useState, useMemo } from 'react'
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu'
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle
} from '@/components/ui/dialog'
import { useCurrency } from '@/lib/currency'
import { cn } from '@/lib/utils'
import { useUpdateOrderStatus, useDeleteOrder } from '@/hooks/use-orders'
import { useGenerateInvoice } from '@/hooks/use-invoices'
import { useToast } from '@/components/ui/use-toast'
import { useAuth } from '@/contexts/auth-context'
import { 
  MoreHorizontal, 
  Eye, 
  Edit, 
  Trash2,
  FileText,
  ChevronLeft,
  ChevronRight,
  ChevronUp,
  ChevronDown
} from 'lucide-react'
import { GenerateInvoiceModal } from './GenerateInvoiceModal'

// Sort configuration
type SortField = 'orderNumber' | 'customerName' | 'date' | 'status' | 'total'
type SortDirection = 'asc' | 'desc'

interface SortConfig {
  field: SortField
  direction: SortDirection
}

// Pagination configuration
interface PaginationConfig {
  currentPage: number
  pageSize: number
  totalItems: number
  totalPages: number
}

// Define the order status type to match our database enum
type OrderStatus = 'Pending payment' | 'Processing' | 'On hold' | 'Completed' | 'Cancelled' | 'Refunded' | 'Failed' | 'Draft'

interface OrderRow {
  id: string
  orderNumber: string
  customerName: string
  date: string
  status: OrderStatus
  total: number
}

interface EnhancedOrderTableProps {
  orders: OrderRow[]
  onOrderView?: (order: OrderRow) => void
  onOrderEdit?: (order: OrderRow) => void
  onOrderDelete?: (orderIds: string[]) => void
  onGenerateInvoice?: (orderId: string) => void
  onSelectedOrdersChange?: (orderIds: string[]) => void
  showBulkActions?: boolean
  defaultPageSize?: number
  isMobile?: boolean
  formatDate?: (dateString: string) => string
}

export function EnhancedOrderTable({
  orders,
  onOrderView,
  onOrderEdit,
  onOrderDelete,
  onGenerateInvoice,
  onSelectedOrdersChange,
  showBulkActions = true,
  defaultPageSize = 10,
  isMobile = false,
  formatDate
}: EnhancedOrderTableProps) {
  const [sortConfig, setSortConfig] = useState<SortConfig>({ 
    field: 'date', 
    direction: 'desc' 
  })
  const [pagination, setPagination] = useState({
    currentPage: 1,
    pageSize: defaultPageSize
  })
  const [selectedOrders, setSelectedOrders] = useState<string[]>([])
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [orderToDelete, setOrderToDelete] = useState<OrderRow | null>(null)
  const [isGenerateInvoiceModalOpen, setIsGenerateInvoiceModalOpen] = useState(false)
  const [orderForInvoice, setOrderForInvoice] = useState<OrderRow | null>(null)
  
  const { user } = useAuth()
  const { userCurrency, formatCurrency } = useCurrency()
  const { toast } = useToast()
  const { mutate: updateOrderStatus } = useUpdateOrderStatus()
  const { mutate: deleteOrder } = useDeleteOrder()
  const { mutate: generateInvoice, isPending: isGeneratingInvoice } = useGenerateInvoice()

  // Default formatDate function if not provided
  const safeFormatDate = formatDate || ((dateString: string) => {
    // Handle undefined or null dateString
    if (!dateString) {
      return 'Unknown date'
    }
    
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch (error) {
      console.error('Error formatting date:', error, 'dateString:', dateString)
      return 'Invalid date'
    }
  })

  // Sort orders based on the current sort configuration
  const sortedOrders = useMemo(() => {
    const sorted = [...orders].sort((a, b) => {
      let aValue: any = a[sortConfig.field as keyof OrderRow]
      let bValue: any = b[sortConfig.field as keyof OrderRow]

      // Handle date fields
      if (sortConfig.field === 'date') {
        aValue = new Date(aValue).getTime()
        bValue = new Date(bValue).getTime()
      }

      // Handle string fields
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase()
        bValue = bValue?.toLowerCase() || ''
      }

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = ''
      if (bValue === null || bValue === undefined) bValue = ''

      if (aValue < bValue) {
        return sortConfig.direction === 'asc' ? -1 : 1
      }
      if (aValue > bValue) {
        return sortConfig.direction === 'asc' ? 1 : -1
      }
      return 0
    })
    return sorted
  }, [orders, sortConfig])

  // Calculate pagination configuration
  const paginationConfig: PaginationConfig = useMemo(() => {
    const totalItems = sortedOrders.length
    const totalPages = Math.ceil(totalItems / pagination.pageSize)
    
    return {
      currentPage: pagination.currentPage,
      pageSize: pagination.pageSize,
      totalItems,
      totalPages
    }
  }, [sortedOrders.length, pagination])

  // Get the orders for the current page
  const paginatedOrders = useMemo(() => {
    const startIndex = (pagination.currentPage - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    const paginated = sortedOrders.slice(startIndex, endIndex)
    return paginated
  }, [sortedOrders, pagination])

  // Handle sorting when a column header is clicked
  const handleSort = (field: SortField) => {
    setSortConfig(current => {
      // If clicking the same field that's already sorted
      if (current.field === field) {
        // If ascending, switch to descending
        if (current.direction === 'asc') {
          return { field, direction: 'desc' };
        } 
        // If descending, reset to default sorting
        else {
          return { field: 'date', direction: 'desc' };
        }
      }
      // If clicking a different field, sort ascending first
      else {
        return { field, direction: 'asc' };
      }
    });
  }

  // Handle pagination when a page is selected
  const handlePageChange = (page: number) => {
    setPagination(current => ({
      ...current,
      currentPage: Math.max(1, Math.min(page, paginationConfig.totalPages))
    }))
    setSelectedOrders([]) // Clear selection when changing pages
  }

  // Handle page size change
  const handlePageSizeChange = (pageSize: number) => {
    setPagination(current => ({
      currentPage: 1,
      pageSize
    }))
    setSelectedOrders([]) // Clear selection when changing page size
  }

  // Handle select all/none for bulk actions
  const handleSelectAll = (checked: boolean) => {
    const newSelection = checked ? paginatedOrders.map(order => order.id) : []
    setSelectedOrders(newSelection)
    onSelectedOrdersChange?.(newSelection)
  }

  // Handle individual order selection
  const handleSelectOrder = (orderId: string, checked: boolean) => {
    const newSelection = checked
      ? [...selectedOrders, orderId]
      : selectedOrders.filter(id => id !== orderId)
    
    setSelectedOrders(newSelection)
    onSelectedOrdersChange?.(newSelection)
  }

  // Handle bulk delete action
  const handleBulkDelete = () => {
    if (selectedOrders.length === 0) return
    
    const selectedOrdersData = orders.filter(order => selectedOrders.includes(order.id))
    
    const confirmMessage = selectedOrders.length === 1
      ? `Are you sure you want to delete order "${selectedOrdersData[0]?.orderNumber}"?\n\nThis action cannot be undone.`
      : `Are you sure you want to delete ${selectedOrders.length} orders?\n\nThis action cannot be undone.`
    
    if (window.confirm(confirmMessage)) {
      try {
        onOrderDelete?.(selectedOrders)
        setSelectedOrders([])
      } catch (error) {
        console.error('Error deleting orders:', error)
        // In a real implementation, we would use toast here
      }
    }
  }

  // Handle generate invoice action - opens the modal
  const handleGenerateInvoiceClick = (order: OrderRow) => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to generate an invoice.",
        variant: "destructive",
      })
      return
    }
    
    setOrderForInvoice(order)
    setIsGenerateInvoiceModalOpen(true)
  }

  // Handle confirm generate invoice from modal
  const handleConfirmGenerateInvoice = (dueDate: Date | undefined, markAsSent: boolean) => {
    if (!orderForInvoice || !user) return
    
    // Show loading toast
    toast({
      title: "Generating Invoice",
      description: "Please wait while we generate the invoice...",
    })
    
    // Call the generate invoice mutation with the order ID and parameters
    generateInvoice({
      orderId: orderForInvoice.id,
      dueDate: dueDate ? dueDate.toISOString() : undefined,
      markAsSent
    }, {
      onSuccess: () => {
        // Close the modal
        setIsGenerateInvoiceModalOpen(false)
        setOrderForInvoice(null)
        
        // Call the parent handler if provided
        onGenerateInvoice?.(orderForInvoice.id)
      },
      onError: (error) => {
        // Update the toast to show error
        toast({
          title: "Error",
          description: `Failed to generate invoice: ${error.message}`,
          variant: "destructive",
        })
      }
    })
  }

  // Handle delete order action
  const handleDeleteOrder = (order: OrderRow) => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to delete an order.",
        variant: "destructive",
      })
      return
    }
    
    const confirmMessage = `Are you sure you want to delete order "${order.orderNumber}"?\n\nThis action cannot be undone.`
    
    if (window.confirm(confirmMessage)) {
      deleteOrder(order.id, {
        onSuccess: () => {
          toast({
            title: "Order Deleted",
            description: `Order ${order.orderNumber} has been successfully deleted.`,
          })
          // Call the parent handler if provided
          onOrderDelete?.([order.id])
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: `Failed to delete order: ${error.message}`,
            variant: "destructive",
          })
        }
      })
    }
  }

  // Handle status update
  const handleStatusUpdate = (orderId: string, orderNumber: string, newStatus: OrderStatus) => {
    updateOrderStatus(
      { orderId, newStatus },
      {
        onSuccess: () => {
          toast({
            title: "Status Updated",
            description: `Order ${orderNumber} status updated to ${newStatus}`,
          })
        },
        onError: (error) => {
          toast({
            title: "Error",
            description: `Failed to update order status: ${error.message}`,
            variant: "destructive",
          })
        }
      }
    )
  }

  // Get sort icon for column headers
  const getSortIcon = (field: SortField) => {
    if (sortConfig.field !== field) {
      return (
        <div className="flex flex-col items-center justify-center ml-1 opacity-50">
          <ChevronUp className="h-3 w-3" />
          <ChevronDown className="h-3 w-3 -mt-1" />
        </div>
      )
    }
    
    if (sortConfig.direction === 'asc') {
      return <ChevronUp className="h-4 w-4 text-blue-600 ml-1" />
    } else {
      return <ChevronDown className="h-4 w-4 text-blue-600 ml-1" />
    }
  }

  // Calculate selection states for the select all checkbox
  const allSelected = paginatedOrders.length > 0 && selectedOrders.length === paginatedOrders.length
  const someSelected = selectedOrders.length > 0 && !allSelected

  // Get status color for the colored dot
  const getStatusColor = (status: OrderStatus) => {
    switch (status) {
      case 'Pending payment':
        return '#facc15' // Yellow
      case 'Processing':
        return '#3b82f6' // Blue
      case 'On hold':
        return '#f97316' // Orange
      case 'Completed':
        return '#22c55e' // Green
      case 'Cancelled':
        return '#94a3b8' // Gray
      case 'Draft':
        return '#94a3b8' // Gray
      case 'Refunded':
        return '#8b5cf6' // Purple
      case 'Failed':
        return '#ef4444' // Red
      default:
        return '#94a3b8' // Gray
    }
  }

  // Get light background color for active dropdown item
  const getLightBackgroundColor = (status: OrderStatus) => {
    switch (status) {
      case 'Pending payment':
        return 'bg-yellow-50' // Light yellow
      case 'Processing':
        return 'bg-blue-50' // Light blue
      case 'On hold':
        return 'bg-orange-50' // Light orange
      case 'Completed':
        return 'bg-green-50' // Light green
      case 'Cancelled':
        return 'bg-gray-50' // Light gray
      case 'Draft':
        return 'bg-gray-50' // Light gray
      case 'Refunded':
        return 'bg-purple-50' // Light purple
      case 'Failed':
        return 'bg-red-50' // Light red
      default:
        return 'bg-gray-50' // Light gray
    }
  }

  // All possible order statuses
  const orderStatuses: OrderStatus[] = [
    'Pending payment',
    'Processing',
    'On hold',
    'Completed',
    'Cancelled',
    'Refunded',
    'Failed',
    'Draft'
  ]

  return (
    <>
      {/* Generate Invoice Modal */}
      {orderForInvoice && (
        <GenerateInvoiceModal
          open={isGenerateInvoiceModalOpen}
          onOpenChange={setIsGenerateInvoiceModalOpen}
          orderNumber={orderForInvoice.orderNumber}
          onConfirm={handleConfirmGenerateInvoice}
          isGenerating={isGeneratingInvoice}
        />
      )}
      
      {/* Bulk Actions - Only show when items are selected */}
      {showBulkActions && selectedOrders.length > 0 && (
        <div className="flex items-center space-x-3 mb-4">
          <span className="text-sm text-muted-foreground">
            {selectedOrders.length} selected
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleBulkDelete}
            className="text-red-600 hover:text-red-700 hover:bg-red-50 border-red-200"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </div>
      )}
      
      {orders.length === 0 ? (
        <div className="text-center py-12 border border-gray-200 rounded-lg">
          <div className="h-16 w-16 mx-auto text-muted-foreground mb-4 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-16 w-16">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
          </div>
          <p className="text-muted-foreground text-lg">No orders to display.</p>
          <p className="text-muted-foreground text-sm mt-2">Create your first order to get started.</p>
        </div>
      ) : (
        <>
          {/* Table Container */}
          <div className="border border-gray-200 rounded-t-lg overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 bg-white">
                <thead className="bg-gray-50">
                  <tr>
                    {showBulkActions && (
                      <th 
                        scope="col" 
                        className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                      >
                        <input
                          type="checkbox"
                          className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                          checked={allSelected}
                          ref={(input) => {
                            if (input) {
                              input.indeterminate = someSelected;
                            }
                          }}
                          onChange={(e) => handleSelectAll(e.target.checked)}
                        />
                      </th>
                    )}
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('orderNumber')}
                        >
                          Order #
                          {getSortIcon('orderNumber')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('customerName')}
                        >
                          Customer
                          {getSortIcon('customerName')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('date')}
                        >
                          Date
                          {getSortIcon('date')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('status')}
                        >
                          Status
                          {getSortIcon('status')}
                        </span>
                      </div>
                    </th>
                    <th 
                      scope="col" 
                      className="text-left py-3 px-6 font-medium text-gray-900 text-xs"
                    >
                      <div className="flex items-center gap-1">
                        <span 
                          className="cursor-pointer hover:bg-gray-100 transition-colors flex items-center"
                          onClick={() => handleSort('total')}
                        >
                          Total
                          {getSortIcon('total')}
                        </span>
                      </div>
                    </th>
                    <th scope="col" className="text-center py-3 px-6 font-medium text-gray-900 w-32 text-xs">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {paginatedOrders.length === 0 ? (
                    <tr>
                      <td colSpan={showBulkActions ? 7 : 6} className="py-8 text-center text-muted-foreground">
                        No orders match the current filters.
                      </td>
                    </tr>
                  ) : (
                    paginatedOrders.map((order) => (
                      <tr 
                        key={order.id}
                        className="hover:bg-gray-50"
                      >
                        {showBulkActions && (
                          <td className="py-4 px-6">
                            <input
                              type="checkbox"
                              className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                              checked={selectedOrders.includes(order.id)}
                              onChange={(e) => handleSelectOrder(order.id, e.target.checked)}
                            />
                          </td>
                        )}
                        <td className="py-4 px-6 text-xs font-medium text-gray-900">
                          {order.orderNumber}
                        </td>
                        <td className="py-4 px-6 text-xs text-gray-700">
                          {order.customerName}
                        </td>
                        <td className="py-4 px-6 text-xs text-gray-700">
                          {safeFormatDate(order.date)}
                        </td>
                        <td className="py-4 px-6">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <div className="flex items-center cursor-pointer">
                                <span 
                                  className="h-2 w-2 rounded-full" 
                                  style={{ backgroundColor: getStatusColor(order.status) }}
                                ></span>
                                <span className="ml-2 text-sm capitalize">{order.status}</span>
                              </div>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="start" className="w-48 p-1">
                              {orderStatuses.map((status) => {
                                const isActive = order.status === status;
                                return (
                                  <DropdownMenuItem
                                    key={status}
                                    onClick={() => handleStatusUpdate(order.id, order.orderNumber, status)}
                                    className={cn(
                                      "relative flex items-center py-2 px-2 cursor-pointer text-sm",
                                      isActive && getLightBackgroundColor(status)
                                    )}
                                  >
                                    {isActive && (
                                      <span 
                                        className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 rounded-r"
                                        style={{ 
                                          backgroundColor: getStatusColor(status),
                                          height: '50%'
                                        }}
                                      ></span>
                                    )}
                                    <span 
                                      className="h-2 w-2 rounded-full mr-2" 
                                      style={{ backgroundColor: getStatusColor(status) }}
                                    ></span>
                                    <span className={cn(
                                      isActive ? "font-semibold" : "font-medium",
                                      isActive ? 
                                        (status === 'Pending payment' ? "text-yellow-800" :
                                         status === 'Processing' ? "text-blue-800" :
                                         status === 'On hold' ? "text-orange-800" :
                                         status === 'Completed' ? "text-green-800" :
                                         status === 'Cancelled' ? "text-gray-800" :
                                         status === 'Draft' ? "text-gray-800" :
                                         status === 'Refunded' ? "text-purple-800" :
                                         "text-red-800") : 
                                        "text-slate-700"
                                    )}>
                                      {status}
                                    </span>
                                  </DropdownMenuItem>
                                );
                              })}
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </td>
                        <td className="py-4 px-6 text-xs font-medium text-gray-900">
                          {formatCurrency(order.total, userCurrency)}
                        </td>
                        <td className="py-4 px-6 text-center">
                          <div className="flex items-center justify-center relative w-full">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant="ghost" className="h-8 w-8 p-0">
                                  <span className="sr-only">Open menu</span>
                                  <MoreHorizontal className="h-4 w-4" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end" className="w-48">
                                <DropdownMenuItem
                                  onClick={() => onOrderView?.(order)}
                                  className="cursor-pointer text-xs py-2"
                                >
                                  <Eye className="h-4 w-4 mr-2" />
                                  <span>View</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                  onClick={() => onOrderEdit?.(order)}
                                  className="cursor-pointer text-xs py-2"
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  <span>Edit</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleGenerateInvoiceClick(order)}
                                  className="cursor-pointer text-xs py-2"
                                >
                                  <FileText className="h-4 w-4 mr-2" />
                                  <span>Generate Invoice</span>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                  onClick={() => handleDeleteOrder(order)}
                                  className="cursor-pointer text-red-600 focus:text-red-600 focus:bg-red-50 text-xs py-2"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  <span>Delete</span>
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </td>
                      </tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>
          
          {/* Pagination */}
          <div className="flex flex-col md:flex-row items-center justify-between space-y-3 md:space-y-0 p-4 border border-gray-200 border-t-0 rounded-b-lg bg-gray-50">
            {/* Left: Items per page selector */}
            <div className="flex items-center gap-2 text-xs text-gray-600">
              <span>Show</span>
              <select
                value={pagination.pageSize}
                onChange={(e) => handlePageSizeChange(Number(e.target.value))}
                className="rounded border border-gray-200 px-2 py-1 text-xs h-7"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={20}>20</option>
                <option value={50}>50</option>
              </select>
              <span>per page</span>
            </div>
            
            {/* Right: Pagination Controls and Stats */}
            <div className="flex items-center w-full md:w-auto justify-end">
              {/* Item Range and Navigation */}
              <div className="flex items-center gap-2">
                <p className="text-xs text-gray-600">
                  <span className="hidden sm:inline">Showing </span>
                  <span className="font-medium">{Math.min((pagination.currentPage - 1) * pagination.pageSize + 1, paginationConfig.totalItems)}-{Math.min(pagination.currentPage * pagination.pageSize, paginationConfig.totalItems)}</span>
                  <span className="hidden sm:inline"> of </span>
                  <span className="sm:hidden">/</span>
                  <span className="font-medium">{paginationConfig.totalItems}</span>
                </p>
                
                <div className="flex items-center rounded-md border border-gray-200 overflow-hidden">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage - 1)}
                    disabled={pagination.currentPage <= 1}
                    className="h-7 w-7 p-0 rounded-none border-r border-gray-200"
                  >
                    <ChevronLeft className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                  
                  <div className="hidden sm:flex">
                    {Array.from({ length: Math.min(3, paginationConfig.totalPages) }, (_, i) => {
                      const page = Math.max(1, pagination.currentPage - 1) + i
                      if (page > paginationConfig.totalPages) return null
                      
                      return (
                        <Button
                          key={page}
                          variant={page === pagination.currentPage ? "default" : "ghost"}
                          size="sm"
                          onClick={() => handlePageChange(page)}
                          className={`h-7 w-7 p-0 rounded-none border-r border-gray-200 text-xs ${
                            page === pagination.currentPage 
                              ? "bg-blue-600 hover:bg-blue-700 text-white" 
                              : "text-gray-700"
                          }`}
                        >
                          {page}
                        </Button>
                      )
                    })}
                  </div>
                  
                  <div className="sm:hidden flex items-center justify-center h-7 w-7 text-xs font-medium border-r border-gray-200">
                    {pagination.currentPage}
                  </div>
                  
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handlePageChange(pagination.currentPage + 1)}
                    disabled={pagination.currentPage >= paginationConfig.totalPages}
                    className="h-7 w-7 p-0 rounded-none"
                  >
                    <ChevronRight className="h-3.5 w-3.5 text-gray-600" />
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  )
}