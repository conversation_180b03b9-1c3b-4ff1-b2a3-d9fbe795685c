'use client'

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { format, parseISO } from 'date-fns'
import { cn } from '@/lib/utils'
import { createSupabaseClient } from '@/lib/supabase'
import {
  CheckCircle,
  AlertTriangle,
  Info,
  X,
  Archive,
  ShoppingCart,
  Package,
  FileText,
  DollarSign,
  Plus,
  Minus
} from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications?: any[]
}

interface ExpenseDetails {
  vendor: string | null
  payment_method: string | null
}

interface ExpandedNotificationDetailsProps {
  notification: Notification
  isGrouped: boolean
  getPanelNotificationIconProps: (type: string) => {
    Icon: any
    bgClass: string
    iconClass: string
  }
  getPriorityBadge: (priority?: string) => JSX.Element | null
  formatDate: (dateString: string) => string
  formatFullDate: (dateString: string) => string
}

export function ExpandedNotificationDetails({
  notification,
  isGrouped,
  getPanelNotificationIconProps,
  getPriorityBadge,
  formatDate,
  formatFullDate
}: ExpandedNotificationDetailsProps) {
  const [expenseDetails, setExpenseDetails] = useState<ExpenseDetails | null>(null)
  const [loading, setLoading] = useState(false)
  const { organizationId } = useAuth()
  const supabase = createSupabaseClient()

  // Check if this is an expense notification
  const isExpenseNotification = notification.type === 'expense_added' && notification.related_entity_id

  // Fetch expense details when it's an expense notification
  useEffect(() => {
    const fetchExpenseDetails = async () => {
      if (!isExpenseNotification || !organizationId || !notification.related_entity_id) return

      setLoading(true)
      try {
        const { data, error } = await supabase
          .from('expenses')
          .select('vendor, payment_method')
          .eq('id', notification.related_entity_id)
          .eq('organization_id', organizationId)
          .single()

        if (error) {
          console.error('Error fetching expense details:', error)
          return
        }

        if (data) {
          setExpenseDetails({
            vendor: data.vendor,
            payment_method: data.payment_method
          })
        }
      } catch (error) {
        console.error('Error fetching expense details:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchExpenseDetails()
  }, [isExpenseNotification, notification.related_entity_id, organizationId])

  return (
    <div className="mt-3 pt-3 border-t border-gray-100">
      {isGrouped ? (
        // Show related notifications for grouped items with a cleaner design
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-gray-700">Related Stock Adjustments</h4>
          <div className="space-y-2">
            {notification.related_notifications!.map((related: Notification, index: number) => {
              return (
                <div key={related.id} className="flex items-start gap-2 text-xs rounded">
                  <div className="flex-1 min-w-0">
                    <p className="text-gray-600">
                      {related.message}
                    </p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      ) : isExpenseNotification ? (
        // Show enhanced expense-specific details with vendor and payment method
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="font-medium text-gray-700">Created:</span>
            <span className="ml-1.5 text-gray-600">
              {notification.created_at ? format(parseISO(notification.created_at), 'MMM d, yyyy h:mm a') : 'Unknown date'}
            </span>
          </div>
          
          {loading ? (
            <div className="col-span-2 text-xs text-gray-500">Loading expense details...</div>
          ) : expenseDetails ? (
            <>
              {expenseDetails.vendor && (
                <div>
                  <span className="font-medium text-gray-700">Vendor Info:</span>
                  <span className="ml-1.5 text-gray-600">{expenseDetails.vendor}</span>
                </div>
              )}
              
              {expenseDetails.payment_method && (
                <div>
                  <span className="font-medium text-gray-700">Payment Method:</span>
                  <span className="ml-1.5 text-gray-600">{expenseDetails.payment_method}</span>
                </div>
              )}
            </>
          ) : null}
        </div>
      ) : (
        // Show standard expanded details for other non-grouped items
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div>
            <span className="font-medium text-gray-700">Type:</span>
            <span className="ml-1.5 text-gray-600">{notification.type}</span>
          </div>
          <div>
            <span className="font-medium text-gray-700">Created:</span>
            <span className="ml-1.5 text-gray-600">
              {notification.created_at ? format(parseISO(notification.created_at), 'MMM d, yyyy h:mm a') : 'Unknown date'}
            </span>
          </div>
          {notification.category && (
            <div>
              <span className="font-medium text-gray-700">Category:</span>
              <span className="ml-1.5 text-gray-600">{notification.category}</span>
            </div>
          )}
          {notification.related_entity_type && (
            <div>
              <span className="font-medium text-gray-700">Related:</span>
              <span className="ml-1.5 text-gray-600 capitalize">
                {notification.related_entity_type}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}