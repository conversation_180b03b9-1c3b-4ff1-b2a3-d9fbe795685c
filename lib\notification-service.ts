import { createSupabaseClient } from '@/lib/supabase';

export class NotificationService {
  private supabase = createSupabaseClient();

  // Create a new notification
  async createNotification(data: {
    organizationId: string;
    type: string;
    title: string;
    message: string;
    relatedEntityType?: string;
    relatedEntityId?: string;
  }) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .insert({
          organization_id: data.organizationId,
          type: data.type,
          title: data.title,
          message: data.message,
          related_entity_type: data.relatedEntityType,
          related_entity_id: data.relatedEntityId,
        });

      if (error) {
        console.error('Error creating notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error creating notification:', error);
      return { error };
    }
  }

  // Check if organization has enabled notifications
  async getOrganizationNotificationPreferences(organizationId: string) {
    try {
      const { data, error } = await this.supabase
        .from('organization_preferences')
        .select('*')
        .eq('organization_id', organizationId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 means no rows returned
        console.error('Error fetching organization preferences:', error);
        // Return default preferences if error
        return {
          email_notifications: true,
          push_notifications: true
        };
      }

      // If no preferences found, return defaults
      if (!data) {
        return {
          email_notifications: true,
          push_notifications: true
        };
      }

      return {
        email_notifications: data.email_notifications ?? true,
        push_notifications: data.push_notifications ?? true
      };
    } catch (error) {
      console.error('Error fetching organization preferences:', error);
      // Return default preferences if error
      return {
        email_notifications: true,
        push_notifications: true
      };
    }
  }

  // Get unread notification count for an organization
  async getUnreadNotificationCount(organizationId: string): Promise<number> {
    try {
      const { count, error } = await this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('organization_id', organizationId)
        .eq('is_read', false);

      if (error) {
        console.error('Error fetching notification count:', error);
        return 0;
      }

      return count || 0;
    } catch (error) {
      console.error('Error fetching notification count:', error);
      return 0;
    }
  }

  // Mark notification as read
  async markAsRead(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as read:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error marking notification as read:', error);
      return { error };
    }
  }

  // Mark notification as unread
  async markAsUnread(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: false })
        .eq('id', notificationId);

      if (error) {
        console.error('Error marking notification as unread:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error marking notification as unread:', error);
      return { error };
    }
  }

  // Mark all notifications as read
  async markAllAsRead(organizationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('organization_id', organizationId)
        .eq('is_read', false);

      if (error) {
        console.error('Error marking all notifications as read:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      return { error };
    }
  }

  // Get notifications for an organization with pagination and filters
  async getNotifications(
    organizationId: string, 
    limit: number = 10, 
    offset: number = 0,
    filters: {
      readStatus?: 'all' | 'unread' | 'read' | 'archived',
      type?: string,
      searchQuery?: string
    } = {}
  ) {
    try {
      let query = this.supabase
        .from('notifications')
        .select('*')
        .eq('organization_id', organizationId)
        .order('created_at', { ascending: false })

      // Apply read status filter
      if (filters.readStatus === 'unread') {
        query = query.eq('is_read', false).eq('is_archived', false)
      } else if (filters.readStatus === 'read') {
        query = query.eq('is_read', true).eq('is_archived', false)
      } else if (filters.readStatus === 'archived') {
        query = query.eq('is_archived', true)
      } else if (filters.readStatus === 'all') {
        // For 'all', show non-archived by default
        query = query.eq('is_archived', false)
      } else {
        // Default: show non-archived
        query = query.eq('is_archived', false)
      }

      // Apply type filter
      if (filters.type && filters.type !== 'all') {
        query = query.eq('type', filters.type)
      }

      // Apply search filter
      if (filters.searchQuery) {
        const searchQuery = filters.searchQuery.toLowerCase()
        query = query.or(`title.ilike.%${searchQuery}%,message.ilike.%${searchQuery}%,type.ilike.%${searchQuery}%`)
      }

      // Apply pagination
      query = query.range(offset, offset + limit - 1)

      const { data, error } = await query

      if (error) {
        console.error('Error fetching notifications:', error)
        return []
      }

      // Filter out any notifications with invalid dates
      const validData = (data || []).filter(notification => {
        if (!notification.created_at) return false
        const date = new Date(notification.created_at)
        return !isNaN(date.getTime())
      })

      return validData
    } catch (error) {
      console.error('Error fetching notifications:', error)
      return []
    }
  }

  // Get total count of notifications for an organization with filters
  async getNotificationCount(
    organizationId: string,
    filters: {
      readStatus?: 'all' | 'unread' | 'read' | 'archived',
      type?: string,
      searchQuery?: string
    } = {}
  ) {
    try {
      let query = this.supabase
        .from('notifications')
        .select('*', { count: 'exact', head: true })
        .eq('organization_id', organizationId)

      // Apply read status filter
      if (filters.readStatus === 'unread') {
        query = query.eq('is_read', false).eq('is_archived', false)
      } else if (filters.readStatus === 'read') {
        query = query.eq('is_read', true).eq('is_archived', false)
      } else if (filters.readStatus === 'archived') {
        query = query.eq('is_archived', true)
      } else if (filters.readStatus === 'all') {
        // For 'all', show non-archived by default
        query = query.eq('is_archived', false)
      } else {
        // Default: show non-archived
        query = query.eq('is_archived', false)
      }

      // Apply type filter
      if (filters.type && filters.type !== 'all') {
        query = query.eq('type', filters.type)
      }

      // Apply search filter
      if (filters.searchQuery) {
        const searchQuery = filters.searchQuery.toLowerCase()
        query = query.or(`title.ilike.%${searchQuery}%,message.ilike.%${searchQuery}%,type.ilike.%${searchQuery}%`)
      }

      const { count, error } = await query

      if (error) {
        console.error('Error fetching notification count:', error)
        return 0
      }

      return count || 0
    } catch (error) {
      console.error('Error fetching notification count:', error)
      return 0
    }
  }

  // Archive a notification
  async archiveNotification(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_archived: true })
        .eq('id', notificationId);

      if (error) {
        console.error('Error archiving notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error archiving notification:', error);
      return { error };
    }
  }

  // Unarchive a notification
  async unarchiveNotification(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .update({ is_archived: false })
        .eq('id', notificationId);

      if (error) {
        console.error('Error unarchiving notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error unarchiving notification:', error);
      return { error };
    }
  }

  // Delete a notification permanently
  async deleteNotification(notificationId: string) {
    try {
      const { error } = await this.supabase
        .from('notifications')
        .delete()
        .eq('id', notificationId);

      if (error) {
        console.error('Error deleting notification:', error);
        return { error };
      }
      
      return { error: null };
    } catch (error) {
      console.error('Error deleting notification:', error);
      return { error };
    }
  }
}