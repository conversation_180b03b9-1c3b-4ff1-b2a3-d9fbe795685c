'use client'

import { useState } from 'react'
import { PageHeader } from '@/components/ui/page-header'
import { ProfitLossTab } from '@/components/reporting/profit-loss/ProfitLossTab'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { DateRange } from 'react-day-picker'
import { format } from 'date-fns'

export default function ProfitLossPage() {
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: new Date(new Date().getFullYear(), 0, 1),
    to: new Date(),
  })

  return (
    <div className="space-y-6">
      {/* Removed duplicate PageHeader to match dashboard spacing standards */}
      <ProfitLossTab dateRange={dateRange} />
    </div>
  )
}