'use client'

import React, { useState, useMemo, useCallback, useEffect } from 'react'
import { OrderTableHeader, INITIAL_ORDER_FILTERS, type OrderFilters } from '@/components/sales/orders/OrderTableHeader'
import { EnhancedOrderTable } from '@/components/sales/orders/EnhancedOrderTable'
import { useAllOrders, useInvalidateOrders } from '@/hooks/use-orders'
import { useAuth } from '@/contexts/auth-context'
import { getSupabaseClient } from '@/lib/supabase'
import { generateInvoiceFromOrder } from '@/lib/supabase-invoices'
import { useToast } from '@/components/ui/use-toast'

interface OrdersTabProps {
  isMobile?: boolean
  className?: string
}

export function OrdersTab({
  isMobile = false,
  className
}: OrdersTabProps) {
  // State for filters
  const [filters, setFilters] = useState<OrderFilters>(INITIAL_ORDER_FILTERS)
  
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  const invalidateOrders = useInvalidateOrders()
  
  // Use React Query to fetch orders
  const { data: orders = [], isLoading, error, refetch } = useAllOrders(organizationId || undefined)
  
  // Memoize the Supabase client instance to prevent re-creation on every render
  const supabase = useMemo(() => getSupabaseClient(), [])
  
  // Listen for order created events to refresh the orders list
  useEffect(() => {
    const handleOrderCreated = () => {
      invalidateOrders.invalidateAllOrders()
    }
    
    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        // When tab becomes visible, refresh the data
        refetch()
      }
    }
    
    window.addEventListener('orderCreated', handleOrderCreated)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    return () => {
      window.removeEventListener('orderCreated', handleOrderCreated)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [invalidateOrders, refetch])
  
  // Transform orders data to match EnhancedOrderTable expectations
  const transformedOrders = useMemo(() => {
    const orderData: any[] = Array.isArray(orders) ? orders : [];
    return orderData.map((order: any) => ({
      id: order.id,
      orderNumber: order.order_number,
      customerName: order.customer_name || 'Unknown Customer',
      date: order.order_date,
      status: order.status?.toLowerCase() || 'pending',
      total: order.total
    }))
  }, [orders])
  
  // Filter orders based on current filters
  const filteredOrders = useMemo(() => {
    let result = [...transformedOrders]
    
    // Text search
    if (filters.searchQuery.trim()) {
      const query = filters.searchQuery.toLowerCase()
      result = result.filter(order => 
        order.orderNumber?.toLowerCase().includes(query) ||
        order.customerName?.toLowerCase().includes(query)
      )
    }
    
    // Status filter
    if (filters.status.length > 0) {
      result = result.filter(order => 
        filters.status.includes(order.status)
      )
    }
    
    // Date range filter
    if (filters.dateRange.from || filters.dateRange.to) {
      result = result.filter(order => {
        const orderDate = new Date(order.date)
        
        if (filters.dateRange.from && orderDate < filters.dateRange.from) {
          return false
        }
        
        if (filters.dateRange.to && orderDate > filters.dateRange.to) {
          return false
        }
        
        return true
      })
    }
    
    return result
  }, [transformedOrders, filters])
  
  // Handle filters change
  const handleFiltersChange = useCallback((newFilters: OrderFilters) => {
    setFilters(newFilters)
  }, [])
  
  // Handle order view
  const handleOrderView = useCallback((order: any) => {
    // In a real implementation, this would open a modal or navigate to order details
  }, [])
  
  // Handle order edit
  const handleOrderEdit = useCallback((order: any) => {
    // In a real implementation, this would open an edit modal
  }, [])
  
  // Handle order delete
  const handleOrderDelete = async (orderIds: string[]) => {
    if (!organizationId) return
    
    try {
      // In a real implementation, this would delete orders from the database
      
      // Refresh the orders list
      refetch()
      
      // Dispatch dashboard refresh event
      window.dispatchEvent(new CustomEvent('dashboardRefresh'))
    } catch (error) {
      console.error('Error deleting orders:', error)
    }
  }
  
  // Handle generate invoice
  const handleGenerateInvoice = async (orderId: string) => {
    if (!organizationId) return
    
    try {
      const invoice = await generateInvoiceFromOrder(organizationId, orderId)
      
      toast({
        title: "Invoice Generated",
        description: `Invoice #${invoice.invoice_number} created successfully`,
      })
      
      // Optionally navigate to the invoices page
      // router.push('/dashboard/sales/invoices')
      
      // Refresh the orders list to update any UI that might need to reflect the change
      refetch()
      
      // Dispatch dashboard refresh event
      window.dispatchEvent(new CustomEvent('dashboardRefresh'))
    } catch (error: any) {
      console.error('Error generating invoice:', error)
      toast({
        title: "Error",
        description: error.message || "Failed to generate invoice. Please try again.",
        variant: "destructive"
      })
    }
  }
  
  // Render loading state
  if (isLoading) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <h3 className="text-lg font-medium">Loading orders...</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Please wait while we fetch your orders.
            </p>
          </div>
        </div>
      </div>
    )
  }
  
  // Render error state
  if (error) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center max-w-md">
            <div className="text-red-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
              <h3 className="text-lg font-medium">Unable to Load Orders</h3>
            </div>
            <p className="text-muted-foreground text-sm mb-4">
              {error instanceof Error ? error.message : 'Failed to load orders. Please try again.'}
            </p>
            <button 
              onClick={() => refetch()} 
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    )
  }
  
  return (
    <div className={className}>
      {/* Integrated Table Header with Filters */}
      <OrderTableHeader
        filters={filters}
        onFiltersChange={handleFiltersChange}
        isMobile={isMobile}
      />
      
      {/* Enhanced Order Table */}
      <div className="mt-4">
        <EnhancedOrderTable
          orders={filteredOrders}
          onOrderView={handleOrderView}
          onOrderEdit={handleOrderEdit}
          onOrderDelete={handleOrderDelete}
          onGenerateInvoice={handleGenerateInvoice}
          showBulkActions={true}
          defaultPageSize={isMobile ? 5 : 10}
          isMobile={isMobile}
          formatDate={(dateString: string) => {
            // Handle undefined or null dateString
            if (!dateString) {
              return 'Unknown date'
            }
            
            try {
              const date = new Date(dateString)
              return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
              })
            } catch (error) {
              console.error('Error formatting date:', error, 'dateString:', dateString)
              return 'Invalid date'
            }
          }}
        />
      </div>
    </div>
  )
}
