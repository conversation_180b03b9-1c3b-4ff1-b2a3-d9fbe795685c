'use client'

import { useState, useEffect, useRef, Dispatch, SetStateAction } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { getSupabaseClient } from '@/lib/supabase'
import { NotificationService } from '@/lib/notification-service'
import { centralizedNotificationService } from '@/lib/centralized-notification-service'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ToastAction } from '@/components/ui/toast'
import { toast } from '@/components/ui/use-toast'
import { useToast } from '@/components/ui/use-toast'
import {
  CheckCircle,
  AlertTriangle,
  Info,
  Archive,
  CheckCheck,
  Trash2,
  EyeOff,
  Search,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal,
  RotateCcw,
  X,
  Filter,
  Clock,
  Calendar,
  ChevronDown,
  ChevronUp,
  ExternalLink,
  Bell,
  Settings,
  Package,
  ShoppingCart,
  DollarSign,
  Plus,
  Minus,
  FileText
} from 'lucide-react'
import { format, formatDistanceToNow, parseISO, isToday, isYesterday, isThisWeek, isThisYear } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/auth-context'
import {
  NotificationHeader,
  NotificationSearchAndFilters,
  NotificationList,
  NotificationPagination,
  BulkActions,
  EmptyState,
  GroupedNotifications,
  NotificationProvider
} from '@/components/notifications'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
}

// New interface for grouped notifications
interface GroupedNotification {
  id: string // ID of the parent notification
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications: Notification[] // Child notifications
}

interface NotificationGroup {
  title: string
  notifications: (Notification | GroupedNotification)[]
  count: number
}

// Define notification types based on the database schema
type NotificationType = 'stock_adjustment' | 'low_stock' | 'out_of_stock' | 'purchase_order' | 'stock_history' | 'product_deleted' | 'all'

// Define undo action types
type UndoAction = {
  type: 'archive' | 'delete'
  notifications: Notification[]
}

export default function NotificationsPage() {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [filteredNotifications, setFilteredNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [readFilter, setReadFilter] = useState<'all' | 'unread' | 'read' | 'archived'>('all')
  const [typeFilter, setTypeFilter] = useState<NotificationType>('all')
  const [searchQuery, setSearchQuery] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [totalNotifications, setTotalNotifications] = useState(0)
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([])
  const [expandedNotifications, setExpandedNotifications] = useState<string[]>([])
  const [highlightedNotification, setHighlightedNotification] = useState<string | null>(null)
  const [viewMode, setViewMode] = useState<'list' | 'cards' | 'grouped'>('cards')
  const [sortBy, setSortBy] = useState<'date' | 'priority' | 'type'>('date')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [showFilters, setShowFilters] = useState(false)
  const notificationsPerPage = 10 // Fixed to 10 items per page

  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast: showToast } = useToast()
  const undoActionRef = useRef<UndoAction | null>(null)
  const lastVisibilityChangeRef = useRef<number>(Date.now())
  const { organizationId } = useAuth()

  const supabase = getSupabaseClient()
  const notificationService = new NotificationService()

  // Handle URL parameters for highlighting and opening specific notifications
  useEffect(() => {
    const highlight = searchParams.get('highlight')
    const notificationId = searchParams.get('notification')

    if (highlight) {
      setHighlightedNotification(highlight)
      // Auto-expand the highlighted notification
      setExpandedNotifications(prev => [...prev, highlight])
      // Scroll to notification after a brief delay
      setTimeout(() => {
        const element = document.getElementById(`notification-${highlight}`)
        if (element) {
          element.scrollIntoView({ behavior: 'smooth', block: 'center' })
        }
      }, 500)
    }
  }, [searchParams, notifications])

  // Initialize filters from URL parameters
  useEffect(() => {
    const urlReadFilter = searchParams.get('status')
    const urlTypeFilter = searchParams.get('type')
    const urlSearchQuery = searchParams.get('search')
    const urlViewMode = searchParams.get('view')
    const urlSortBy = searchParams.get('sort')
    const urlSortOrder = searchParams.get('order')

    if (urlReadFilter && ['all', 'unread', 'read', 'archived'].includes(urlReadFilter)) {
      setReadFilter(urlReadFilter as any)
    }
    if (urlTypeFilter) {
      setTypeFilter(urlTypeFilter as any)
    }
    if (urlSearchQuery) {
      setSearchQuery(urlSearchQuery)
    }
    if (urlViewMode && ['list', 'cards', 'grouped'].includes(urlViewMode)) {
      setViewMode(urlViewMode as any)
    }
    if (urlSortBy && ['date', 'priority', 'type'].includes(urlSortBy)) {
      setSortBy(urlSortBy as any)
    }
    if (urlSortOrder && ['asc', 'desc'].includes(urlSortOrder)) {
      setSortOrder(urlSortOrder as any)
    }
  }, [])

  // Update URL when filters change
  useEffect(() => {
    const url = new URL(window.location.href)

    // Update filter parameters
    if (readFilter !== 'all') {
      url.searchParams.set('status', readFilter)
    } else {
      url.searchParams.delete('status')
    }

    if (typeFilter !== 'all') {
      url.searchParams.set('type', typeFilter)
    } else {
      url.searchParams.delete('type')
    }

    if (searchQuery) {
      url.searchParams.set('search', searchQuery)
    } else {
      url.searchParams.delete('search')
    }

    if (viewMode !== 'cards') {
      url.searchParams.set('view', viewMode)
    } else {
      url.searchParams.delete('view')
    }

    if (sortBy !== 'date') {
      url.searchParams.set('sort', sortBy)
    } else {
      url.searchParams.delete('sort')
    }

    if (sortOrder !== 'desc') {
      url.searchParams.set('order', sortOrder)
    } else {
      url.searchParams.delete('order')
    }

    // Update URL without triggering a page reload
    window.history.replaceState({}, '', url.toString())
  }, [readFilter, typeFilter, searchQuery, viewMode, sortBy, sortOrder])

  // Load notifications
  useEffect(() => {
    loadNotifications()

    // Set up subscription using centralized service
    let unsubscribe: (() => void) | null = null
    if (organizationId) {
      unsubscribe = centralizedNotificationService.subscribe(
        organizationId,
        (newNotification: any) => {
          // Add new notification to the top of the list only if it doesn't already exist
          setNotifications(prev => {
            // Check if notification already exists
            const exists = prev.some(notification => notification.id === newNotification.id)
            if (!exists) {
              return [newNotification, ...prev]
            }
            return prev
          })
          // Reset to first page when new notification arrives
          setCurrentPage(1)
        },
        (updatedNotification: any) => {
          // Update notification in the list
          setNotifications(prev =>
            prev.map(notification =>
              notification.id === updatedNotification.id
                ? updatedNotification
                : notification
            )
          )
        },
        () => {} // No delete handler needed for this component
      )
    }

    // Handle visibility change to refresh when tab becomes visible
    const handleVisibilityChange = () => {
      // Check if we're in the browser environment
      if (typeof document === 'undefined') return;
      
      const now = Date.now()
      // Debounce visibility changes
      if (now - lastVisibilityChangeRef.current < 1000) {
        return
      }
      
      lastVisibilityChangeRef.current = now
      
      if (document.visibilityState === 'visible') {
        console.debug('NotificationsPage: Tab became visible, refreshing data')
        // When tab becomes visible, refresh the data
        loadNotifications()
        // Force reconnection of notification service
        if (organizationId) {
          centralizedNotificationService.reconnect(organizationId)
        }
      }
    }

    // Handle window focus
    const handleFocus = () => {
      // Check if we're in the browser environment
      if (typeof window === 'undefined') return;
      
      console.debug('NotificationsPage: Window focused, refreshing data')
      loadNotifications()
      // Force reconnection of notification service
      if (organizationId) {
        centralizedNotificationService.reconnect(organizationId)
      }
    }

    // Only add event listeners if we're in the browser environment
    if (typeof document !== 'undefined' && typeof window !== 'undefined') {
      document.addEventListener('visibilitychange', handleVisibilityChange)
      window.addEventListener('focus', handleFocus)
    }

    return () => {
      // Only remove event listeners if we're in the browser environment
      if (typeof document !== 'undefined' && typeof window !== 'undefined') {
        if (unsubscribe) {
          unsubscribe()
        }
        document.removeEventListener('visibilitychange', handleVisibilityChange)
        window.removeEventListener('focus', handleFocus)
      }
    }
  }, [organizationId])

  // Load notifications when filters or page changes
  useEffect(() => {
    loadNotifications()
  }, [currentPage, readFilter, typeFilter, searchQuery, organizationId])

  const loadNotifications = async () => {
    try {
      setLoading(true)
      if (organizationId) {
        // Implement proper server-side pagination with filters
        const offset = (currentPage - 1) * notificationsPerPage
        const notifications = await notificationService.getNotifications(
          organizationId, 
          notificationsPerPage, 
          offset,
          {
            readStatus: readFilter,
            type: typeFilter !== 'all' ? typeFilter : undefined,
            searchQuery: searchQuery || undefined
          }
        )
        setNotifications(notifications)
        
        // Also get the total count for pagination with the same filters
        const totalCount = await notificationService.getNotificationCount(
          organizationId,
          {
            readStatus: readFilter,
            type: typeFilter !== 'all' ? typeFilter : undefined,
            searchQuery: searchQuery || undefined
          }
        )
        setTotalNotifications(totalCount)
      }
    } catch (error) {
      console.error('Error loading notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  // Group related notifications together
  const groupRelatedNotifications = (notifications: Notification[]): (Notification | GroupedNotification)[] => {
    const grouped: Record<string, GroupedNotification> = {}
    const ungrouped: Notification[] = []
    
    // Process notifications to group orders with their related stock adjustments
    notifications.forEach(notification => {
      // Check if this is a "New Order Created" notification
      if (notification.type === 'new_order' && notification.related_entity_id) {
        // This is a parent notification
        const orderId = notification.related_entity_id
        
        // Create the grouped notification
        const groupedNotification: GroupedNotification = {
          ...notification,
          related_notifications: []
        }
        
        // Find stock adjustments that are likely related to this order
        // We use a time-based heuristic since we don't have direct database access here
        // In a production environment, this would be done with a proper database query
        const orderTime = new Date(notification.created_at).getTime()
        const timeWindow = 5 * 60 * 1000 // 5 minutes window
        
        // Look for related stock adjustments that occurred shortly after the order
        notifications.forEach(possibleStockAdjustment => {
          if (possibleStockAdjustment.type === 'stock_adjustment' && 
              possibleStockAdjustment.related_entity_id) {
            const adjustmentTime = new Date(possibleStockAdjustment.created_at).getTime()
            
            // Check if this stock adjustment happened after the order and within the time window
            // This indicates it's likely related to fulfilling this order
            if (adjustmentTime > orderTime && adjustmentTime <= orderTime + timeWindow) {
              groupedNotification.related_notifications.push(possibleStockAdjustment)
            }
          }
        })
        
        grouped[orderId] = groupedNotification
      }
    })
    
    // Process all notifications again to determine what should be grouped vs ungrouped
    notifications.forEach(notification => {
      // Check if this notification is already part of a group
      let isAlreadyGrouped = false
      for (const orderId in grouped) {
        if (grouped[orderId].id === notification.id) {
          // This is a parent notification that has been grouped
          isAlreadyGrouped = true
          break
        }
        if (grouped[orderId].related_notifications.some(related => related.id === notification.id)) {
          // This is a child notification that has been grouped
          isAlreadyGrouped = true
          break
        }
      }
      
      // If not already grouped, add to ungrouped notifications
      if (!isAlreadyGrouped) {
        ungrouped.push(notification)
      }
    })
    
    // Convert grouped object to array and combine with ungrouped notifications
    const result: (Notification | GroupedNotification)[] = [...ungrouped]
    Object.values(grouped).forEach(group => {
      // Only add groups that have related notifications
      if (group.related_notifications.length > 0) {
        result.push(group)
      } else {
        // If no related notifications, treat as regular notification
        result.push({
          ...group,
          related_notifications: undefined
        } as unknown as Notification)
      }
    })
    
    // Sort by date, newest first
    return result.sort((a, b) => {
      const dateA = new Date(a.created_at).getTime()
      const dateB = new Date(b.created_at).getTime()
      return dateB - dateA
    })
  }

  // Group notifications by time periods
  const groupNotifications = (notifications: (Notification | GroupedNotification)[]): NotificationGroup[] => {
    const groups: NotificationGroup[] = []
    const today: (Notification | GroupedNotification)[] = []
    const yesterday: (Notification | GroupedNotification)[] = []
    const thisWeek: (Notification | GroupedNotification)[] = []
    const older: (Notification | GroupedNotification)[] = []

    notifications.forEach(notification => {
      const date = new Date(notification.created_at)
      if (isToday(date)) {
        today.push(notification)
      } else if (isYesterday(date)) {
        yesterday.push(notification)
      } else if (Date.now() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
        thisWeek.push(notification)
      } else {
        older.push(notification)
      }
    })

    if (today.length > 0) groups.push({ title: 'Today', notifications: today, count: today.length })
    if (yesterday.length > 0) groups.push({ title: 'Yesterday', notifications: yesterday, count: yesterday.length })
    if (thisWeek.length > 0) groups.push({ title: 'This Week', notifications: thisWeek, count: thisWeek.length })
    if (older.length > 0) groups.push({ title: 'Older', notifications: older, count: older.length })

    return groups
  }

  // Enhanced sorting function
  const sortNotifications = (notifications: (Notification | GroupedNotification)[]): (Notification | GroupedNotification)[] => {
    return [...notifications].sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'date':
          comparison = new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
          break
        case 'priority':
          const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 }
          const aPriority = priorityOrder[(a as Notification).priority as keyof typeof priorityOrder] || 0
          const bPriority = priorityOrder[(b as Notification).priority as keyof typeof priorityOrder] || 0
          comparison = aPriority - bPriority
          break
        case 'type':
          comparison = a.type.localeCompare(b.type)
          break
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })
  }

  // Toggle notification expansion
  const toggleNotificationExpansion = (notificationId: string) => {
    setExpandedNotifications(prev =>
      prev.includes(notificationId)
        ? prev.filter(id => id !== notificationId)
        : [...prev, notificationId]
    )
  }

  // Generate shareable URL for a specific notification
  const generateNotificationUrl = (notificationId: string, highlight: boolean = false) => {
    const url = new URL(window.location.origin + '/dashboard/notifications')
    if (highlight) {
      url.searchParams.set('highlight', notificationId)
    } else {
      url.searchParams.set('notification', notificationId)
    }
    return url.toString()
  }

  // Navigate to notification with highlighting
  const navigateToNotification = (notificationId: string) => {
    const url = generateNotificationUrl(notificationId, true)
    router.push(url)
  }

  const markAsRead = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsRead(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: true } 
            : notification
        )
      )
      
      // Broadcast the change to other components
      centralizedNotificationService.broadcastEvent('notification-updated', {
        id,
        is_read: true
      })
    } catch (error) {
      console.error('Error marking notification as read:', error)
    }
  }

  const markAsUnread = async (id: string) => {
    try {
      // Update database
      await notificationService.markAsUnread(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_read: false } 
            : notification
        )
      )
      
      // Broadcast the change to other components
      centralizedNotificationService.broadcastEvent('notification-updated', {
        id,
        is_read: false
      })
    } catch (error) {
      console.error('Error marking notification as unread:', error)
    }
  }

  const archiveNotification = async (id: string) => {
    try {
      // Update database
      await notificationService.archiveNotification(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === id 
            ? { ...notification, is_archived: true } 
            : notification
        )
      )
      
      // Broadcast the change to other components
      centralizedNotificationService.broadcastEvent('notification-updated', {
        id,
        is_archived: true
      })
      
      // Show undo toast
      toast({
        title: "Notification Archived",
        description: "The notification has been archived.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                await notificationService.unarchiveNotification(id)
                
                // Update local state
                setNotifications(prev => 
                  prev.map(notification => 
                    notification.id === id 
                      ? { ...notification, is_archived: false } 
                      : notification
                  )
                )
                
                // Broadcast the change to other components
                centralizedNotificationService.broadcastEvent('notification-updated', {
                  id,
                  is_archived: false
                })
                
                showToast({
                  title: "Notification Restored",
                  description: "The notification has been restored."
                })
              } catch (error) {
                console.error('Error unarchiving notification:', error)
                showToast({
                  title: "Error",
                  description: "Failed to restore notification. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error archiving notification:', error)
      showToast({
        title: "Error",
        description: "Failed to archive notification. Please try again.",
        variant: "destructive"
      })
    }
  }

  const deleteNotification = async (id: string) => {
    try {
      // Store notification for potential undo
      const notificationToDelete = notifications.find(n => n.id === id)
      if (notificationToDelete) {
        undoActionRef.current = {
          type: 'delete',
          notifications: [notificationToDelete]
        }
      }
      
      // Update database
      await notificationService.deleteNotification(id)
      
      // Update local state immediately for instant feedback
      setNotifications(prev => prev.filter(notification => notification.id !== id))
      
      // Broadcast the change to other components
      centralizedNotificationService.broadcastEvent('notification-deleted', {
        id
      })
      
      // Show undo toast
      toast({
        title: "Notification Deleted",
        description: "The notification has been deleted.",
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notification
                // For now, we'll just show a message
                showToast({
                  title: "Delete Undone",
                  description: "In a real implementation, this would restore the notification."
                })
              } catch (error) {
                console.error('Error undoing delete:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo delete. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error deleting notification:', error)
      showToast({
        title: "Error",
        description: "Failed to delete notification. Please try again.",
        variant: "destructive"
      })
    }
  }

  const markAllAsRead = async () => {
    try {
      if (organizationId) {
        // Update database
        await notificationService.markAllAsRead(organizationId)
        
        // Update local state immediately for instant feedback
        setNotifications(prev => 
          prev.map(notification => ({ ...notification, is_read: true }))
        )
        
        // Broadcast the change to other components
        centralizedNotificationService.broadcastEvent('notifications-marked-as-read', { organizationId })
        
        showToast({
          title: "All Notifications Marked as Read",
          description: "All your notifications have been marked as read."
        })
      }
    } catch (error) {
      console.error('Error marking all notifications as read:', error)
      showToast({
        title: "Error",
        description: "Failed to mark all notifications as read. Please try again.",
        variant: "destructive"
      })
    }
  }

  const archiveSelected = async () => {
    try {
      // Store notifications for potential undo
      const notificationsToArchive = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'archive',
        notifications: notificationsToArchive
      }
      
      // Update database for each selected notification
      await Promise.all(
        selectedNotifications.map(id => notificationService.archiveNotification(id))
      )
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.map(notification => 
          selectedNotifications.includes(notification.id) 
            ? { ...notification, is_archived: true } 
            : notification
        )
      )
      
      // Broadcast the changes to other components
      selectedNotifications.forEach(id => {
        centralizedNotificationService.broadcastEvent('notification-updated', {
          id,
          is_archived: true
        })
      })
      
      // Clear selection
      setSelectedNotifications([])
      
      // Show undo toast
      toast({
        title: `${selectedNotifications.length} Notification${selectedNotifications.length > 1 ? 's' : ''} Archived`,
        description: `${selectedNotifications.length} notification${selectedNotifications.length > 1 ? 's have' : ' has'} been archived.`,
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notifications
                // For now, we'll just show a message
                showToast({
                  title: "Archive Undone",
                  description: "In a real implementation, this would restore the notifications."
                })
              } catch (error) {
                console.error('Error undoing archive:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo archive. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error archiving selected notifications:', error)
      showToast({
        title: "Error",
        description: "Failed to archive selected notifications. Please try again.",
        variant: "destructive"
      })
    }
  }

  const deleteSelected = async () => {
    try {
      // Store notifications for potential undo
      const notificationsToDelete = notifications.filter(n => selectedNotifications.includes(n.id))
      undoActionRef.current = {
        type: 'delete',
        notifications: notificationsToDelete
      }
      
      // Update database for each selected notification
      await Promise.all(
        selectedNotifications.map(id => notificationService.deleteNotification(id))
      )
      
      // Update local state immediately for instant feedback
      setNotifications(prev => 
        prev.filter(notification => !selectedNotifications.includes(notification.id))
      )
      
      // Broadcast the changes to other components
      selectedNotifications.forEach(id => {
        centralizedNotificationService.broadcastEvent('notification-deleted', {
          id
        })
      })
      
      // Clear selection
      setSelectedNotifications([])
      
      // Show undo toast
      toast({
        title: `${selectedNotifications.length} Notification${selectedNotifications.length > 1 ? 's' : ''} Deleted`,
        description: `${selectedNotifications.length} notification${selectedNotifications.length > 1 ? 's have' : ' has'} been deleted.`,
        action: (
          <ToastAction 
            altText="Undo" 
            onClick={async () => {
              try {
                // In a real implementation, you would restore the notifications
                // For now, we'll just show a message
                showToast({
                  title: "Delete Undone",
                  description: "In a real implementation, this would restore the notifications."
                })
              } catch (error) {
                console.error('Error undoing delete:', error)
                showToast({
                  title: "Error",
                  description: "Failed to undo delete. Please try again.",
                  variant: "destructive"
                })
              }
            }}
          >
            Undo
          </ToastAction>
        )
      })
    } catch (error) {
      console.error('Error deleting selected notifications:', error)
      showToast({
        title: "Error",
        description: "Failed to delete selected notifications. Please try again.",
        variant: "destructive"
      })
    }
  }

  const toggleNotificationSelection = (id: string) => {
    setSelectedNotifications(prev => 
      prev.includes(id) 
        ? prev.filter(notificationId => notificationId !== id) 
        : [...prev, id]
    )
  }

  const toggleSelectAll = () => {
    if (selectedNotifications.length === currentNotifications.length) {
      setSelectedNotifications([])
    } else {
      setSelectedNotifications(currentNotifications.map(notification => notification.id))
    }
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />
      case 'product_deleted':
        return <X className="h-4 w-4 text-red-500" />
      case 'overdue_invoice':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'new_order':
        return <Info className="h-4 w-4 text-blue-500" />
      case 'info':
      default:
        return <Info className="h-4 w-4 text-blue-500" />
    }
  }

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success':
        return 'border-l-green-500'
      case 'warning':
        return 'border-l-yellow-500'
      case 'product_deleted':
        return 'border-l-red-500'
      case 'overdue_invoice':
        return 'border-l-red-500'
      case 'new_order':
        return 'border-l-blue-500'
      case 'info':
      default:
        return 'border-l-blue-500'
    }
  }

  // Get enhanced notification icon with background
  const getPanelNotificationIconProps = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'bg-green-100',
          iconClass: 'text-green-600',
          borderClass: 'border-green-200'
        }
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-yellow-100',
          iconClass: 'text-yellow-600',
          borderClass: 'border-yellow-200'
        }
      case 'product_deleted':
        return {
          Icon: X,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-600',
          borderClass: 'border-red-200'
        }
      case 'overdue_invoice':
        return {
          Icon: AlertTriangle,
          bgClass: 'bg-red-100',
          iconClass: 'text-red-600',
          borderClass: 'border-red-200'
        }
      case 'new_order':
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-600',
          borderClass: 'border-blue-200'
        }
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'bg-blue-100',
          iconClass: 'text-blue-600',
          borderClass: 'border-blue-200'
        }
    }
  }

  // Get priority badge styling
  const getPriorityBadge = (priority?: string) => {
    if (!priority) return null

    const variants = {
      low: { variant: 'secondary' as const, text: 'Low', color: 'text-gray-600' },
      medium: { variant: 'default' as const, text: 'Medium', color: 'text-blue-600' },
      high: { variant: 'destructive' as const, text: 'High', color: 'text-orange-600' },
      urgent: { variant: 'destructive' as const, text: 'Urgent', color: 'text-red-600' }
    }

    const config = variants[priority as keyof typeof variants]
    if (!config) return null

    return (
      <Badge variant={config.variant} className={cn("text-xs", config.color)}>
        {config.text}
      </Badge>
    )
  }

  // Calculate pagination
  const indexOfLastNotification = currentPage * notificationsPerPage
  const indexOfFirstNotification = indexOfLastNotification - notificationsPerPage
  const totalPages = Math.ceil(totalNotifications / notificationsPerPage)

  const goToNextPage = () => {
    if (currentPage < totalPages) {
      setCurrentPage(prev => prev + 1)
    }
  }

  const goToPrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(prev => prev - 1)
    }
  }

  const formatDate = (dateString: string) => {
    // Handle undefined or null dateString
    if (!dateString) {
      return 'Unknown date'
    }
    
    try {
      const date = parseISO(dateString)
      
      if (isToday(date)) {
        return format(date, 'h:mm a')
      } else if (isYesterday(date)) {
        return 'Yesterday'
      } else if (isThisWeek(date)) {
        return format(date, 'EEEE')
      } else if (isThisYear(date)) {
        return format(date, 'MMM d')
      } else {
        return format(date, 'MMM d, yyyy')
      }
    } catch (error) {
      console.error('Error formatting date:', error, 'dateString:', dateString)
      return 'Invalid date'
    }
  }

  const formatFullDate = (dateString: string) => {
    // Handle undefined or null dateString
    if (!dateString) {
      return 'Unknown date'
    }
    
    try {
      const date = parseISO(dateString)
      return format(date, 'MMMM d, yyyy h:mm a')
    } catch (error) {
      console.error('Error formatting full date:', error, 'dateString:', dateString)
      return 'Invalid date'
    }
  }

  // Loading state with skeleton
  if (loading) {
    return (
      <div className="container mx-auto py-6 space-y-6">
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-8 w-48 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-32 bg-gray-200 rounded animate-pulse" />
          </div>
          <div className="h-10 w-32 bg-gray-200 rounded animate-pulse" />
        </div>

        <div className="grid gap-4">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="p-6 animate-fade-in-up" style={{ animationDelay: `${i * 100}ms` }}>
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 skeleton-pulse rounded-full" />
                <div className="flex-1 space-y-3">
                  <div className="h-5 skeleton-pulse rounded" />
                  <div className="h-4 skeleton-pulse rounded w-3/4" />
                  <div className="h-3 skeleton-pulse rounded w-1/2" />
                  <div className="flex items-center gap-2 mt-3">
                    <div className="h-6 w-16 skeleton-pulse rounded-full" />
                    <div className="h-6 w-20 skeleton-pulse rounded-full" />
                  </div>
                </div>
                <div className="flex gap-1">
                  <div className="w-8 h-8 skeleton-pulse rounded" />
                  <div className="w-8 h-8 skeleton-pulse rounded" />
                  <div className="w-8 h-8 skeleton-pulse rounded" />
                </div>
              </div>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  // Process notifications for display
  const relatedGroupedNotifications = groupRelatedNotifications(notifications)
  const sortedNotifications = sortNotifications(relatedGroupedNotifications)
  const groupedNotifications = viewMode === 'grouped' ? groupNotifications(sortedNotifications) : []
  const currentNotifications = viewMode === 'grouped'
    ? sortedNotifications
    : sortedNotifications // Use sortedNotifications directly since we're doing server-side pagination
    
  // Update total notifications count for pagination
  // For non-grouped view, we need to calculate the actual display count
  // which accounts for grouped notifications being displayed as single items
  const totalDisplayNotifications = viewMode === 'grouped' 
    ? totalNotifications 
    : relatedGroupedNotifications.length // Use the length of grouped notifications for accurate count
  
  const totalNotificationsCount = totalNotifications // Use the server-side count

  // Get notification icon for panel style
  const getPanelNotificationIconPropsPanel = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'product_deleted':
        return {
          Icon: Archive,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'overdue_invoice':
      return {
        Icon: AlertTriangle,
        bgClass: 'border border-gray-200',
        iconClass: 'text-gray-800',
      };
      case 'new_order':
        return {
          Icon: ShoppingCart,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'stock_adjustment':
        return {
          Icon: Package,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'low_stock':
        return {
          Icon: AlertTriangle,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'out_of_stock':
        return {
          Icon: X,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'purchase_order':
        return {
          Icon: FileText,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'expense_added':
        return {
          Icon: DollarSign,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'product_added':
        return {
          Icon: Plus,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'border border-gray-200',
          iconClass: 'text-gray-800',
        };
    }
  }

  // Wrapper functions to fix type mismatches
  const setTypeFilterWrapper = (filter: string) => {
    setTypeFilter(filter as NotificationType)
  }

  const setSortByWrapper = (sort: string) => {
    setSortBy(sort as 'date' | 'priority' | 'type')
  }

  // Create context value
  const contextValue = {
    // State
    notifications,
    filteredNotifications,
    loading,
    readFilter,
    typeFilter,
    searchQuery,
    currentPage,
    totalNotifications,
    selectedNotifications,
    expandedNotifications,
    highlightedNotification,
    viewMode,
    sortBy,
    sortOrder,
    showFilters,
    notificationsPerPage,
    
    // Functions
    setReadFilter,
    setTypeFilter: setTypeFilterWrapper,
    setSearchQuery,
    setCurrentPage,
    setTotalNotifications,
    setSelectedNotifications,
    setExpandedNotifications,
    setHighlightedNotification,
    setViewMode,
    setSortBy: setSortByWrapper,
    setSortOrder,
    setShowFilters,
    toggleNotificationSelection,
    toggleSelectAll,
    toggleNotificationExpansion,
    markAsRead,
    markAsUnread,
    archiveNotification,
    deleteNotification,
    markAllAsRead,
    archiveSelected,
    deleteSelected,
    loadNotifications,
    formatDate,
    formatFullDate,
    getPanelNotificationIconProps: getPanelNotificationIconPropsPanel,
    getPriorityBadge,
    groupRelatedNotifications,
    sortNotifications,
    groupNotifications,
  }

  return (
    <NotificationProvider value={contextValue}>
      <div className="container mx-auto py-6 space-y-4">
        {/* Header with Title and Actions */}
        <NotificationHeader
          totalNotificationsCount={totalDisplayNotifications}
          markAllAsRead={markAllAsRead}
          allRead={notifications.every(n => n.is_read)}
        />

        {/* Search and Filters */}
        <NotificationSearchAndFilters
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          showFilters={showFilters}
          setShowFilters={setShowFilters}
          readFilter={readFilter}
          setReadFilter={setReadFilter}
          typeFilter={typeFilter}
          setTypeFilter={setTypeFilterWrapper}
          sortBy={sortBy}
          setSortBy={setSortByWrapper}
          sortOrder={sortOrder}
          setSortOrder={setSortOrder}
          viewMode={viewMode}
          setViewMode={setViewMode}
        />

        {/* Bulk Actions */}
        <BulkActions
          selectedNotifications={selectedNotifications}
          currentNotifications={currentNotifications}
          toggleSelectAll={toggleSelectAll}
          archiveSelected={archiveSelected}
          deleteSelected={deleteSelected}
          clearSelection={() => setSelectedNotifications([])}
        />

        {/* Notifications List */}
        {currentNotifications.length === 0 ? (
          <EmptyState
            searchQuery={searchQuery}
            readFilter={readFilter}
            typeFilter={typeFilter}
            clearFilters={() => {
              setSearchQuery('')
              setReadFilter('all')
              setTypeFilter('all')
            }}
          />
        ) : viewMode === 'grouped' ? (
          // Grouped View
          <GroupedNotifications
            groupedNotifications={groupedNotifications}
            expandedNotifications={expandedNotifications}
            toggleNotificationExpansion={toggleNotificationExpansion}
            formatDate={formatDate}
            getPanelNotificationIconProps={getPanelNotificationIconPropsPanel}
            getPriorityBadge={getPriorityBadge}
            formatFullDate={formatFullDate}
          />
        ) : (
          // Cards/List View - Modern compact design matching notification panel
          <NotificationList
            notifications={currentNotifications}
            expandedNotifications={expandedNotifications}
            toggleNotificationExpansion={toggleNotificationExpansion}
            formatDate={formatDate}
            getPanelNotificationIconProps={getPanelNotificationIconPropsPanel}
            getPriorityBadge={getPriorityBadge}
            formatFullDate={formatFullDate}
          />
        )}

        {/* Pagination */}
        {viewMode !== 'grouped' && totalDisplayNotifications > 0 && (
          <NotificationPagination
            currentPage={currentPage}
            totalPages={Math.ceil(totalDisplayNotifications / notificationsPerPage)}
            indexOfFirstNotification={indexOfFirstNotification}
            indexOfLastNotification={Math.min(indexOfLastNotification, totalDisplayNotifications)}
            totalFilteredNotifications={totalDisplayNotifications}
            goToPrevPage={goToPrevPage}
            goToNextPage={goToNextPage}
            setCurrentPage={setCurrentPage}
          />
        )}
      </div>
    </NotificationProvider>
  )
}