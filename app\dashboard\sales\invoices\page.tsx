'use client'

import InvoicesTab from '@/components/sales/invoices/InvoicesTab'
import { PageHeader } from '@/components/ui/page-header'
import { Button } from '@/components/ui/button'
import { FileSpreadsheet } from 'lucide-react'

export default function InvoicesPage() {
  // Placeholder function for export functionality
  const handleExport = () => {
    console.log('Export functionality would go here')
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <PageHeader 
          title="Invoices"
          description="Manage customer invoices"
        />
        
        <div className="flex items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="flex items-center gap-1.5 h-8 px-3"
            onClick={handleExport}
          >
            <FileSpreadsheet className="h-4 w-4" />
            <span>Export CSV</span>
          </Button>
        </div>
      </div>
      
      <InvoicesTab />
    </div>
  )
}