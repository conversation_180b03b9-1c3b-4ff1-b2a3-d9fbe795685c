// Design system configuration for the redesigned product creation modal
// Following modern UX principles with consistent spacing, typography, and colors

export const designSystem = {
  // Spacing system based on 4px grid
  spacing: {
    xs: '4px',    // gap-1
    sm: '8px',    // gap-2  
    md: '16px',   // gap-4
    lg: '24px',   // gap-6
    xl: '32px',   // gap-8
    '2xl': '48px' // gap-12
  },

  // Typography hierarchy
  typography: {
    // Section headings
    sectionTitle: 'text-lg font-semibold text-gray-900',
    
    // Subsection headings
    subsectionTitle: 'text-base font-medium text-gray-800',
    
    // Form labels
    label: 'text-sm font-medium text-gray-700',
    
    // Body text
    body: 'text-sm text-gray-600',
    
    // Captions and help text
    caption: 'text-xs text-gray-500',
    
    // Error text
    error: 'text-xs text-red-600',
    
    // Success text
    success: 'text-xs text-green-600'
  },

  // Component sizing
  components: {
    // Form fields - compact for modal
    input: {
      height: 'h-8',
      textSize: 'text-xs',
      padding: 'px-3 py-1.5',
      borderRadius: 'rounded-md'
    },
    
    // Buttons
    button: {
      primary: 'bg-blue-600 hover:bg-blue-700 text-white font-medium',
      secondary: 'border border-gray-300 bg-white hover:bg-gray-50 text-gray-700',
      destructive: 'bg-red-600 hover:bg-red-700 text-white font-medium',
      ghost: 'hover:bg-gray-100 text-gray-600',
      height: 'h-8',
      textSize: 'text-xs',
      padding: 'px-3'
    },
    
    // Cards
    card: {
      base: 'rounded-lg border border-gray-200 bg-white shadow-sm',
      header: 'p-4 border-b border-gray-200',
      content: 'p-4',
      footer: 'p-4 border-t border-gray-200'
    },
    
    // Badges
    badge: {
      base: 'inline-flex items-center px-2 py-1 rounded-md text-xs font-medium',
      primary: 'bg-blue-100 text-blue-800',
      secondary: 'bg-gray-100 text-gray-800',
      success: 'bg-green-100 text-green-800',
      warning: 'bg-yellow-100 text-yellow-800',
      error: 'bg-red-100 text-red-800'
    }
  },

  // Color palette
  colors: {
    // Primary actions
    primary: {
      50: '#eff6ff',
      100: '#dbeafe', 
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8'
    },
    
    // Gray scale
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827'
    },
    
    // Status colors
    success: {
      100: '#dcfce7',
      600: '#16a34a',
      800: '#166534'
    },
    
    error: {
      100: '#fee2e2',
      600: '#dc2626',
      800: '#991b1b'
    },
    
    warning: {
      100: '#fef3c7',
      600: '#d97706',
      800: '#92400e'
    }
  },

  // Layout constants
  layout: {
    // Modal dimensions
    modal: {
      width: 'max-w-5xl',
      height: 'h-[85vh]',
      padding: 'p-0'
    },
    
    // Navigation sidebar
    sidebar: {
      width: 'w-64',
      background: 'bg-gray-50',
      borderRight: 'border-r border-gray-200'
    },
    
    // Content area
    content: {
      padding: 'p-6',
      background: 'bg-white'
    }
  },

  // Animation and transitions
  animations: {
    // Standard transitions
    transition: 'transition-all duration-200 ease-in-out',
    
    // Hover effects
    hover: 'hover:shadow-md hover:border-gray-300',
    
    // Focus states
    focus: 'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
    
    // Loading states
    pulse: 'animate-pulse'
  }
} as const

// Helper functions for consistent styling
export const getInputClasses = (hasError?: boolean) => {
  const base = `${designSystem.components.input.height} ${designSystem.components.input.textSize} ${designSystem.components.input.padding} ${designSystem.components.input.borderRadius} border bg-white ${designSystem.animations.transition} ${designSystem.animations.focus}`
  
  if (hasError) {
    return `${base} border-red-300 focus:ring-red-500`
  }
  
  return `${base} border-gray-300 focus:ring-blue-500`
}

export const getButtonClasses = (variant: 'primary' | 'secondary' | 'destructive' | 'ghost' = 'primary') => {
  const base = `${designSystem.components.button.height} ${designSystem.components.button.textSize} ${designSystem.components.button.padding} rounded-md ${designSystem.animations.transition} ${designSystem.animations.focus}`
  
  return `${base} ${designSystem.components.button[variant]}`
}

export const getCardClasses = () => designSystem.components.card.base

export const getBadgeClasses = (variant: 'primary' | 'secondary' | 'success' | 'warning' | 'error' = 'primary') => {
  return `${designSystem.components.badge.base} ${designSystem.components.badge[variant]}`
}
