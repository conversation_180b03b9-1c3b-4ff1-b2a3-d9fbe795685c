'use client'

import { useState, useEffect, useMemo } from 'react'
import { Button, type ButtonProps } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { useToast } from '@/components/ui/use-toast'
import { DatePicker } from '@/components/ui/date-picker'
import { CustomDropdown, CustomDropdownOption } from '@/components/ui/custom-dropdown'
import { SimpleDropdown, SimpleDropdownOption } from '@/components/ui/simple-dropdown'
import { createSupabaseClient, type ExpenseInsert } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { Plus, Minus, ChevronDown, ChevronUp } from 'lucide-react'

// Default categories
const DEFAULT_CATEGORIES: CustomDropdownOption[] = [
  { value: 'Office', label: 'Office Supplies' },
  { value: 'Travel', label: 'Travel & Transportation' },
  { value: 'Meals', label: 'Meals & Entertainment' },
  { value: 'Software', label: 'Software & Subscriptions' },
  { value: 'Marketing', label: 'Marketing & Advertising' },
  { value: 'Utilities', label: 'Utilities' },
  { value: 'Equipment', label: 'Equipment & Hardware' },
  { value: 'Professional', label: 'Professional Services' },
  { value: 'Insurance', label: 'Insurance' },
  { value: 'General', label: 'General' },
]

// Default payment methods
const DEFAULT_PAYMENT_METHODS: CustomDropdownOption[] = [
  { value: 'Cash', label: 'Cash' },
  { value: 'Credit Card', label: 'Credit Card' },
  { value: 'Debit Card', label: 'Debit Card' },
  { value: 'Bank Transfer', label: 'Bank Transfer' },
]

interface AddExpenseFormProps {
  onExpenseAdded?: () => void
  isCollapsible?: boolean
  initiallyExpanded?: boolean
  className?: string
}

export function AddExpenseForm({ 
  className,
  isCollapsible = false,
  onExpenseAdded
}: AddExpenseFormProps) {
  const [isExpanded, setIsExpanded] = useState(true)
  const [hasBeenUsed, setHasBeenUsed] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [amount, setAmount] = useState('')
  const [description, setDescription] = useState('')
  const [vendor, setVendor] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [expenseDate, setExpenseDate] = useState<Date>(new Date())
  const [nextExpenseId, setNextExpenseId] = useState('EXP-001')
  const [categories, setCategories] = useState<CustomDropdownOption[]>(DEFAULT_CATEGORIES)
  const [paymentMethods, setPaymentMethods] = useState<CustomDropdownOption[]>(DEFAULT_PAYMENT_METHODS)
  
  // Memoize dropdown options to prevent excessive re-rendering
  const memoizedCategories = useMemo(() => categories, [categories])
  const memoizedPaymentMethods = useMemo(() => paymentMethods, [paymentMethods])
  
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()
  const { getCurrencySymbol } = useCurrency()

  // Load categories and payment methods on component mount
  useEffect(() => {
    if (user && organizationId) {
      loadCategories()
      loadPaymentMethods()
      loadNextExpenseId()
    }
  }, [user, organizationId])

  const loadCategories = async () => {
    if (!user?.id || !organizationId) return
    
    try {
      const { data, error } = await supabase
        .from('expense_categories')
        .select('id, name')
        .eq('organization_id', organizationId)
        .order('name')

      if (error) {
        console.error('Error loading expense categories:', error)
        return
      }

      if (data && data.length > 0) {
        const categoryOptions = data.map(cat => ({
          value: cat.name,
          label: cat.name
        }))
        setCategories([...DEFAULT_CATEGORIES, ...categoryOptions])
      }
    } catch (error) {
      console.error('Error loading expense categories:', error)
    }
  }

  const loadPaymentMethods = async () => {
    if (!user?.id || !organizationId) return
    
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('payment_method')
        .eq('organization_id', organizationId)
        .not('payment_method', 'is', null)
        .order('payment_method')

      if (error) {
        console.error('Error loading payment methods:', error)
        return
      }

      if (data && data.length > 0) {
        const uniquePaymentMethods = Array.from(new Set(data.map(expense => expense.payment_method)))
        const paymentMethodOptions = uniquePaymentMethods.map((method, index) => ({
          value: method,
          label: method,
          key: `db-${method}-${index}` // Add unique key to prevent duplicates
        }))
        
        // Merge with default options, avoiding duplicates
        const mergedOptions = [...DEFAULT_PAYMENT_METHODS]
        paymentMethodOptions.forEach(dbOption => {
          if (!mergedOptions.some(defaultOption => defaultOption.value === dbOption.value)) {
            mergedOptions.push(dbOption)
          }
        })
        
        setPaymentMethods(mergedOptions)
      }
    } catch (error) {
      console.error('Error loading payment methods:', error)
    }
  }

  const loadNextExpenseId = async () => {
    if (!user?.id || !organizationId) return
    
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('expense_id')
        .eq('organization_id', organizationId)
        .like('expense_id', 'EXP-%')
        .order('created_at', { ascending: false })
        .limit(1)

      if (error) {
        console.error('Error loading expense ID:', error)
        return
      }

      if (data && data.length > 0) {
        const lastId = data[0]?.expense_id || 'EXP-000'
        const match = lastId.match(/EXP-([0-9]+)/)
        if (match) {
          const nextNumber = parseInt(match[1]) + 1
          setNextExpenseId(`EXP-${nextNumber.toString().padStart(3, '0')}`)
        }
      }
    } catch (error) {
      console.error('Error loading next expense ID:', error)
    }
  }

  const handleAddCategory = async (newCategory: CustomDropdownOption) => {
    console.log('Adding new expense category:', newCategory)
    
    // Check if category already exists
    if (categories.find(cat => cat.value === newCategory.value)) {
      console.log('Category already exists:', newCategory.value)
      return
    }
    
    // Add to UI first for immediate feedback
    setCategories(prev => [...prev, newCategory])
    
    // Save to database
    try {
      const { data, error } = await supabase
        .from('expense_categories')
        .insert({
          organization_id: organizationId,
          name: newCategory.value,
          description: ''
        })
        .select()
        
      if (error) {
        console.error('Error saving new expense category:', error)
        // Revert UI change on error
        setCategories(prev => prev.filter(cat => cat.value !== newCategory.value))
        toast({
          title: "Error",
          description: "Failed to save new category. Please try again.",
          variant: "destructive",
        })
      } else {
        console.log('New expense category saved:', data)
        toast({
          title: "Success",
          description: `Category "${newCategory.value}" added successfully.`,
        })
      }
    } catch (error) {
      console.error('Error saving new expense category:', error)
      // Revert UI change on error
      setCategories(prev => prev.filter(cat => cat.value !== newCategory.value))
      toast({
        title: "Error",
        description: "Failed to save new category. Please try again.",
        variant: "destructive",
      })
    }
  }

  const handleAddPaymentMethod = (newPaymentMethod: CustomDropdownOption) => {
    console.log('Adding new payment method:', newPaymentMethod)
    if (!paymentMethods.find(method => method.value === newPaymentMethod.value)) {
      setPaymentMethods(prev => {
        const updated = [...prev, newPaymentMethod]
        console.log('Updated payment methods:', updated)
        return updated
      })
    } else {
      console.log('Payment method already exists:', newPaymentMethod.value)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user || !organizationId) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to record expenses.",
        variant: "destructive",
      })
      return
    }

    if (!amount || !description || !selectedCategory || !selectedPaymentMethod) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      const { data, error } = await supabase
        .from('expenses')
        .insert({
          organization_id: organizationId,
          amount: parseFloat(amount),
          description: description.trim(),
          vendor: vendor.trim() || null,
          category: selectedCategory,
          payment_method: selectedPaymentMethod,
          expense_date: expenseDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
        })
        .select()

      if (error) {
        console.error('Supabase error:', error)
        toast({
          title: "Error Recording Expense",
          description: `Failed to save expense: ${error.message}`,
          variant: "destructive",
        })
      } else {
        console.log('Expense recorded:', data)
        toast({
          title: "Expense Recorded!",
          description: `Successfully recorded $${amount} expense: ${description}`,
        })
        
        // Reset form
        setAmount('')
        setDescription('')
        setVendor('')
        setSelectedCategory('')
        setSelectedPaymentMethod('')
        setExpenseDate(new Date())
        
        // Update next expense ID
        loadNextExpenseId()
        
        // Reload categories and payment methods to include any new ones
        loadCategories()
        loadPaymentMethods()
        
        // Mark as used and collapse if collapsible
        if (isCollapsible && !hasBeenUsed) {
          setHasBeenUsed(true)
          setIsExpanded(false)
        }
        
        // Notify parent component
        onExpenseAdded?.()
        
        // Trigger dashboard refresh by dispatching a custom event
        window.dispatchEvent(new CustomEvent('expenseAdded', { 
          detail: { 
            amount: parseFloat(amount),
            category: selectedCategory,
            description: description.trim()
          } 
        }))
      }
    } catch (error) {
      console.error('Error:', error)
      toast({
        title: "Unexpected Error",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className={className}>
      <CardHeader 
        className={`px-4 py-3 border-b border-gray-200 ${isCollapsible ? "cursor-pointer" : ""}`}
        onClick={() => isCollapsible && setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-base font-medium text-gray-900">
              Add New Expense
            </CardTitle>
          </div>
          {isCollapsible && (
            <Button variant="ghost" size="sm" type="button" className="h-8 text-xs">
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          )}
        </div>
      </CardHeader>
      {(isExpanded || !isCollapsible) && (
        <CardContent className="p-4">
          <form onSubmit={handleSubmit} className="space-y-3">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* Date Picker */}
            <div className="space-y-2">
              <Label htmlFor="expense-date" className="text-xs">Expense Date</Label>
              <DatePicker
                date={expenseDate}
                onDateChange={(date) => setExpenseDate(date || new Date())}
                placeholder="Select expense date"
                className="h-8 text-xs placeholder:text-xs"
              />
            </div>

            {/* Expense ID Preview */}
            <div className="space-y-2">
              <Label className="text-xs">Expense ID</Label>
              <div className="flex h-8 w-full rounded-md border border-input bg-muted px-3 py-2 text-xs text-muted-foreground">
                {nextExpenseId} (auto-generated)
              </div>
            </div>

            {/* Amount */}
            <div className="space-y-2">
              <Label htmlFor="amount" className="text-xs">Amount ({getCurrencySymbol()}) *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                placeholder="Enter amount"
                required
                className="h-8 text-xs placeholder:text-xs"
              />
            </div>

            {/* Category Dropdown */}
            <div className="space-y-2">
              <Label htmlFor="category" className="text-xs">Category *</Label>
              <SimpleDropdown
                options={memoizedCategories as SimpleDropdownOption[]}
                value={selectedCategory}
                onValueChange={(newValue) => {
                  setSelectedCategory(newValue)
                }}
                onAddCustomOption={(newOption) => handleAddCategory(newOption as CustomDropdownOption)}
                placeholder="Select or add category"
                customPlaceholder="Add custom category"
                allowCustom={true}
                className="h-8 text-xs placeholder:text-xs"
              />
            </div>

            {/* Description */}
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="description" className="text-xs">Description *</Label>
              <Input
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Enter description"
                required
                className="h-8 text-xs placeholder:text-xs"
              />
            </div>

            {/* Vendor */}
            <div className="space-y-2">
              <Label htmlFor="vendor" className="text-xs">Vendor/Supplier</Label>
              <Input
                id="vendor"
                value={vendor}
                onChange={(e) => setVendor(e.target.value)}
                placeholder="Enter vendor name"
                className="h-8 text-xs placeholder:text-xs"
              />
            </div>

            {/* Payment Method Dropdown */}
            <div className="space-y-2">
              <Label htmlFor="payment-method" className="text-xs">Payment Method *</Label>
              <CustomDropdown
                options={memoizedPaymentMethods}
                value={selectedPaymentMethod}
                onValueChange={(newValue) => {
                  setSelectedPaymentMethod(newValue)
                }}
                onAddCustomOption={handleAddPaymentMethod}
                placeholder="Select payment method"
                customPlaceholder="Add custom payment method"
                allowCustom={true}
                className="h-8 text-xs placeholder:text-xs"
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-2 pt-2">
            <Button 
              type="submit" 
              size="sm"
              className="h-8 text-xs"
              disabled={isLoading}
            >
              {isLoading ? 'Recording Expense...' : 'Record Expense'}
            </Button>
          </div>
        </form>
        </CardContent>
      )}
    </Card>
  )
}