'use client'

import { But<PERSON> } from '@/components/ui/button'
import { CheckCheck } from 'lucide-react'

interface NotificationHeaderProps {
  totalNotificationsCount: number
  markAllAsRead: () => void
  allRead: boolean
}

export function NotificationHeader({
  totalNotificationsCount,
  markAllAsRead,
  allRead
}: NotificationHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <div>
        <h1 className="text-xl font-bold text-gray-900">Notifications</h1>
        <p className="text-xs text-gray-500 mt-1">
          {totalNotificationsCount > 0
            ? `${totalNotificationsCount} total notification${totalNotificationsCount > 1 ? 's' : ''}`
            : 'No notifications to display'}
        </p>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={markAllAsRead}
          disabled={allRead}
          className="flex items-center gap-2 h-8 px-3 text-xs"
        >
          <CheckCheck className="h-3.5 w-3.5" />
          Mark all read
        </Button>
      </div>
    </div>
  )
}