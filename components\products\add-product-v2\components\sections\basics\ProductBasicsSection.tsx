'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Separator } from '@/components/ui/separator'
import { ProductTypeSelector } from './ProductTypeSelector'
import { EssentialDetailsForm } from './EssentialDetailsForm'
import { CategorySupplierForm } from './CategorySupplierForm'
import { designSystem, getCardClasses } from '../../../config/design-system'

export function ProductBasicsSection() {
  return (
    <div className="space-y-6">
      {/* Product Type Selection */}
      <Card className={getCardClasses()}>
        <CardContent className="p-6">
          <ProductTypeSelector />
        </CardContent>
      </Card>

      {/* Essential Details */}
      <Card className={getCardClasses()}>
        <CardContent className="p-6">
          <EssentialDetailsForm />
        </CardContent>
      </Card>

      {/* Category and Supplier */}
      <Card className={getCardClasses()}>
        <CardContent className="p-6">
          <CategorySupplierForm />
        </CardContent>
      </Card>
    </div>
  )
}