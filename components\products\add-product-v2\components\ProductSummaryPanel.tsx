import React from 'react'
import { useProductForm } from '../context/ProductFormContext'

export function ProductSummaryPanel() {
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="px-4 sm:px-6 py-4 border-b border-white/10">
        <h2 className="text-sm font-medium text-white/90">
          Product Summary
        </h2>
      </div>

      {/* Product Summary Content - Full Column */}
      <div className="flex-1 overflow-y-auto">
        <ProductSummary />
      </div>
    </div>
  )
}

function ProductSummary() {
  const { formData } = useProductForm();

  // Empty state: Displays before the user enters a product name.
  if (!formData.name) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-white/10 flex items-center justify-center">
            <svg className="w-8 h-8 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
            </svg>
          </div>
          <p className="text-sm text-white/70 text-shadow">
            Product details will appear here as you fill out the form.
          </p>
        </div>
      </div>
    );
  }

  // Main component render with data - Full column design
  return (
    <div className="h-full flex flex-col">
      {/* Product Header - Full width */}
      <div className="px-6 py-6 border-b border-white/10">
        <h3 className="text-lg font-semibold text-white text-shadow truncate mb-2">
          {formData.name}
        </h3>
        {formData.base_sku && (
          <p className="text-sm text-white/80 text-shadow font-mono bg-white/10 px-3 py-1 rounded-md inline-block">
            {formData.base_sku}
          </p>
        )}
        <div className="mt-3 flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${formData.has_variants ? 'bg-purple-400' : 'bg-blue-400'}`}></div>
          <span className="text-sm text-white/80 text-shadow">
            {formData.has_variants ? 'Variable Product' : 'Simple Product'}
          </span>
        </div>
      </div>

      {/* Scrollable Content Area */}
      <div className="flex-1 overflow-y-auto">
        {/* Financials Section */}
        {(formData.price !== null && formData.price !== undefined) || formData.base_cost > 0 || formData.packaging_cost > 0 ? (
          <div className="px-6 py-4 border-b border-white/10">
            <h4 className="text-sm font-medium text-white/90 text-shadow mb-3">Financial Overview</h4>
            <div className="space-y-3">
              {formData.price !== null && formData.price !== undefined && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-white/70 text-shadow">Selling Price</span>
                  <span className="text-sm font-semibold text-green-300 text-shadow">
                    ${formData.price.toFixed(2)}
                  </span>
                </div>
              )}
              {(formData.base_cost > 0 || formData.packaging_cost > 0) && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-white/70 text-shadow">Total Cost</span>
                  <span className="text-sm font-medium text-orange-300 text-shadow">
                    ${((formData.base_cost || 0) + (formData.packaging_cost || 0)).toFixed(2)}
                  </span>
                </div>
              )}
              {formData.price && (formData.base_cost > 0 || formData.packaging_cost > 0) && (
                <div className="flex justify-between items-center pt-2 border-t border-white/10">
                  <span className="text-sm font-medium text-white/80 text-shadow">Profit Margin</span>
                  <span className="text-sm font-semibold text-blue-300 text-shadow">
                    ${(formData.price - ((formData.base_cost || 0) + (formData.packaging_cost || 0))).toFixed(2)}
                  </span>
                </div>
              )}
            </div>
          </div>
        ) : null}

        {/* Attributes Section */}
        {(formData.has_variants && formData.variant_attributes.length > 0) ||
         (!formData.has_variants && (formData.size || formData.color)) ? (
          <div className="px-6 py-4 border-b border-white/10">
            <h4 className="text-sm font-medium text-white/90 text-shadow mb-3">Product Attributes</h4>
            <div className="space-y-3">
              {!formData.has_variants ? (
                // Simple product attributes
                <>
                  {formData.size && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-white/70 text-shadow">Size</span>
                      <span className="text-sm font-medium text-white/90 text-shadow bg-white/10 px-2 py-1 rounded">
                        {formData.size}
                      </span>
                    </div>
                  )}
                  {formData.color && (
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-white/70 text-shadow">Color</span>
                      <span className="text-sm font-medium text-white/90 text-shadow bg-white/10 px-2 py-1 rounded">
                        {formData.color}
                      </span>
                    </div>
                  )}
                </>
              ) : (
                // Variable product attributes
                formData.variant_attributes.map((attr, index) => (
                  <div key={index} className="space-y-2">
                    <span className="text-sm font-medium text-white/80 text-shadow capitalize">{attr.name}</span>
                    <div className="flex flex-wrap gap-1">
                      {attr.values.map((value, valueIndex) => (
                        <span key={valueIndex} className="text-xs text-white/90 text-shadow bg-white/10 px-2 py-1 rounded">
                          {value}
                        </span>
                      ))}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>
        ) : null}

        {/* Inventory Section */}
        {formData.stock_quantity !== undefined && !formData.has_variants && (
          <div className="px-6 py-4 border-b border-white/10">
            <h4 className="text-sm font-medium text-white/90 text-shadow mb-3">Inventory</h4>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-sm text-white/70 text-shadow">Stock Quantity</span>
                <span className="text-sm font-semibold text-white text-shadow">
                  {formData.stock_quantity} units
                </span>
              </div>
              {formData.low_stock_threshold && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-white/70 text-shadow">Low Stock Alert</span>
                  <span className="text-sm font-medium text-yellow-300 text-shadow">
                    {formData.low_stock_threshold} units
                  </span>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Additional Info Section */}
        {(formData.brand || formData.category_id || formData.description) && (
          <div className="px-6 py-4">
            <h4 className="text-sm font-medium text-white/90 text-shadow mb-3">Additional Details</h4>
            <div className="space-y-3">
              {formData.brand && (
                <div className="flex justify-between items-center">
                  <span className="text-sm text-white/70 text-shadow">Brand</span>
                  <span className="text-sm font-medium text-white/90 text-shadow">
                    {formData.brand}
                  </span>
                </div>
              )}
              {formData.description && (
                <div className="space-y-1">
                  <span className="text-sm text-white/70 text-shadow">Description</span>
                  <p className="text-sm text-white/90 text-shadow bg-white/5 p-3 rounded-md">
                    {formData.description}
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
