import React from 'react'
import { useProductForm } from '../context/ProductFormContext'

export function ProductSummaryPanel() {
  return (
    <div className="flex flex-col h-full">
      {/* Header */}
      <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-white/10">
        <h2 className="text-sm font-medium text-white/90">
          Product Summary
        </h2>
      </div>

      {/* Product Summary Content */}
      <div className="flex-1 px-4 sm:px-6 py-3 sm:py-4 overflow-y-auto">
        <ProductSummary />
      </div>
    </div>
  )
}

function ProductSummary() {
  const { formData } = useProductForm();

  // Empty state: Displays before the user enters a product name.
  // This has also been styled to match the glassmorphism theme.
  if (!formData.name) {
    return (
      <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 shadow-lg p-4">
        <div className="text-center py-4">
          <p className="text-xs text-white/70 text-shadow">
            Product details will appear here as you fill out the form.
          </p>
        </div>
      </div>
    );
  }

  // Main component render with data.
  return (
    <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 shadow-lg">
      {/* Header Section */}
      <div className="p-4">
        <p className="text-base font-semibold text-white text-shadow truncate">
          {formData.name}
        </p>
        {formData.base_sku && (
          <p className="text-xs text-white/80 text-shadow font-mono">
            {formData.base_sku}
          </p>
        )}
      </div>

      {/* Financials Section */}
      <div className="border-t border-white/10 p-4 space-y-3">
        {formData.price !== null && formData.price !== undefined && (
          <div className="flex justify-between items-center">
            <p className="text-xs text-white/70 text-shadow">Price</p>
            <p className="text-xs font-semibold text-white text-shadow">
              ${formData.price.toFixed(2)}
            </p>
          </div>
        )}
        {(formData.base_cost > 0 || formData.packaging_cost > 0) && (
          <div className="flex justify-between items-center">
            <p className="text-xs text-white/70 text-shadow">Total Cost</p>
            <p className="text-xs font-medium text-white/90 text-shadow">
              ${((formData.base_cost || 0) + (formData.packaging_cost || 0)).toFixed(2)}
            </p>
          </div>
        )}
      </div>

      {/* Attributes Section */}
      {(formData.has_variants && formData.variant_attributes.length > 0) || 
       (!formData.has_variants && (formData.size || formData.color)) ? (
        <div className="border-t border-white/10 p-4 space-y-3">
          <p className="text-xs font-medium text-white/80 text-shadow">Attributes</p>
          {!formData.has_variants ? (
            // Simple product attributes
            <>
              {formData.size && (
                <div className="flex justify-between items-center">
                  <p className="text-xs text-white/70 text-shadow">Size</p>
                  <p className="text-xs font-medium text-white/90 text-shadow">{formData.size}</p>
                </div>
              )}
              {formData.color && (
                <div className="flex justify-between items-center">
                  <p className="text-xs text-white/70 text-shadow">Color</p>
                  <p className="text-xs font-medium text-white/90 text-shadow">{formData.color}</p>
                </div>
              )}
            </>
          ) : (
            // Variable product attributes
            formData.variant_attributes.map((attr, index) => (
              <div key={index} className="flex justify-between items-center">
                <p className="text-xs text-white/70 text-shadow capitalize">{attr.name}</p>
                <p className="text-xs font-medium text-white/90 text-shadow">
                  {attr.values.join(', ')}
                </p>
              </div>
            ))
          )}
        </div>
      ) : null}

      {/* Inventory Section */}
      {(formData.stock_quantity !== undefined || formData.has_variants) && (
        <div className="border-t border-white/10 p-4 space-y-3">
          <div className="flex justify-between items-center">
            <p className="text-xs text-white/70 text-shadow">Type</p>
            <p className="text-xs font-medium text-white/90 text-shadow">
                {formData.has_variants ? 'Variable' : 'Simple'}
            </p>
          </div>
          {formData.stock_quantity !== undefined && !formData.has_variants && (
            <div className="flex justify-between items-center">
              <p className="text-xs text-white/70 text-shadow">Stock</p>
              <p className="text-xs font-semibold text-white text-shadow">
                {formData.stock_quantity} units
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
