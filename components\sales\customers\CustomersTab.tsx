'use client'

import { useState, useMemo } from 'react'
import { useAuth } from '@/contexts/auth-context'
import { useAllCustomers } from '@/hooks/use-customers'
import { CustomerTableHeader } from '@/components/sales/customers/CustomerTableHeader'
import { EnhancedCustomerTable } from '@/components/sales/customers/EnhancedCustomerTable'

interface CustomersTabProps {
  isMobile?: boolean
  className?: string
}

export function CustomersTab({
  isMobile = false,
  className = ''
}: CustomersTabProps) {
  const { organizationId } = useAuth()
  const { data: customers = [], isLoading, isError } = useAllCustomers(organizationId || undefined)
  const [searchTerm, setSearchTerm] = useState('')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)

  // Transform customer data to match EnhancedCustomerTable expectations and filter
  const filteredCustomers = useMemo(() => {
    return customers
      .map(customer => ({
        id: customer.id,
        name: customer.full_name || 'Unnamed Customer',
        email: customer.email || '',
        totalOrders: customer.totalOrders || 0,
        totalSpent: customer.totalSpent || 0
      }))
      .filter(customer => 
        (customer.name?.toLowerCase().includes(searchTerm.toLowerCase()) || '') ||
        (customer.email?.toLowerCase().includes(searchTerm.toLowerCase()) || '')
      )
  }, [customers, searchTerm])

  // Paginate the filtered customers
  const paginatedCustomers = useMemo(() => {
    const startIndex = (currentPage - 1) * pageSize
    const endIndex = startIndex + pageSize
    return filteredCustomers.slice(startIndex, endIndex)
  }, [filteredCustomers, currentPage, pageSize])

  const handleFilterClick = () => {
    // TODO: Implement filter functionality
    console.log('Filter clicked')
  }

  const handleEditCustomer = (customer: any) => {
    // TODO: Implement edit customer functionality
    console.log('Edit customer', customer)
  }

  const handleDeleteCustomer = (customerId: string) => {
    // TODO: Implement delete customer functionality
    console.log('Delete customer', customerId)
  }

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handlePageSizeChange = (size: number) => {
    setPageSize(size)
    setCurrentPage(1) // Reset to first page when changing page size
  }

  if (isLoading) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 mx-auto mb-4 animate-spin" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              <h3 className="text-lg font-medium">Loading customers...</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              Please wait while we fetch your customers.
            </p>
          </div>
        </div>
      </div>
    )
  }

  if (isError) {
    return (
      <div className={className}>
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="text-gray-500 mb-2">
              <h3 className="text-lg font-medium">Error loading customers</h3>
            </div>
            <p className="text-muted-foreground text-sm">
              There was an error loading customers. Please try again later.
            </p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Consistent spacing with other pages */}
        <CustomerTableHeader
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          onFilterClick={handleFilterClick}
          activeFilterCount={0}
          isMobile={isMobile}
        />
        
        <div className="mt-4">
          <EnhancedCustomerTable
            customers={paginatedCustomers}
            isMobile={isMobile}
            onEditCustomer={handleEditCustomer}
            onDeleteCustomer={handleDeleteCustomer}
            currentPage={currentPage}
            pageSize={pageSize}
            totalItems={filteredCustomers.length}
            onPageChange={handlePageChange}
            onPageSizeChange={handlePageSizeChange}
          />
        </div>
      </div>
    </div>
  )
}