'use client'

import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Search, SlidersHorizontal, RotateCcw, ChevronDown, ChevronUp } from 'lucide-react'

interface NotificationSearchAndFiltersProps {
  searchQuery: string
  setSearchQuery: (query: string) => void
  showFilters: boolean
  setShowFilters: (show: boolean) => void
  readFilter: 'all' | 'unread' | 'read' | 'archived'
  setReadFilter: (filter: 'all' | 'unread' | 'read' | 'archived') => void
  typeFilter: string
  setTypeFilter: (filter: string) => void
  sortBy: string
  setSortBy: (sort: string) => void
  sortOrder: 'asc' | 'desc'
  setSortOrder: (order: 'asc' | 'desc') => void
  viewMode: 'list' | 'cards' | 'grouped'
  setViewMode: (mode: 'list' | 'cards' | 'grouped') => void
}

export function NotificationSearchAndFilters({
  searchQuery,
  setSearchQuery,
  showFilters,
  setShowFilters,
  readFilter,
  setReadFilter,
  typeFilter,
  setTypeFilter,
  sortBy,
  setSortBy,
  sortOrder,
  setSortOrder,
  viewMode,
  setViewMode
}: NotificationSearchAndFiltersProps) {
  // Calculate active filter count
  const activeFilterCount = (() => {
    let count = 0
    if (readFilter !== 'all') count++
    if (typeFilter !== 'all') count++
    return count
  })()

  return (
    <>
      {/* Search Bar - Modern compact design */}
      <div className="flex flex-col sm:flex-row gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3.5 w-3.5 text-gray-500" />
          <Input
            type="text"
            placeholder="Search notifications..."
            className="pl-8 h-8 text-xs"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {/* Filter Button - Adjacent to search bar */}
        <Button
          variant="outline"
          size="sm"
          onClick={() => setShowFilters(!showFilters)}
          className="h-8 px-3 text-xs flex items-center gap-1.5"
        >
          <SlidersHorizontal className="h-3.5 w-3.5" />
          Filters
          {activeFilterCount > 0 ? (
            <span className="ml-1.5 flex items-center justify-center w-4 h-4 rounded-full bg-red-500 text-white text-[10px] font-medium">
              {activeFilterCount}
            </span>
          ) : null}
        </Button>
      </div>

      {/* Filters Panel */}
      {showFilters && (
        <Card className="border border-gray-200 bg-white">
          <CardContent className="p-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
              <div className="space-y-1.5">
                <Label className="text-xs font-medium">Status</Label>
                <Select value={readFilter} onValueChange={(value: any) => setReadFilter(value)}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all" className="text-xs">All notifications</SelectItem>
                    <SelectItem value="unread" className="text-xs">Unread only</SelectItem>
                    <SelectItem value="read" className="text-xs">Read only</SelectItem>
                    <SelectItem value="archived" className="text-xs">Archived</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1.5">
                <Label className="text-xs font-medium">Type</Label>
                <Select value={typeFilter} onValueChange={(value: any) => setTypeFilter(value)}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all" className="text-xs">All types</SelectItem>
                    <SelectItem value="stock_adjustment" className="text-xs">Stock Adjustment</SelectItem>
                    <SelectItem value="low_stock" className="text-xs">Low Stock</SelectItem>
                    <SelectItem value="out_of_stock" className="text-xs">Out of Stock</SelectItem>
                    <SelectItem value="purchase_order" className="text-xs">Purchase Order</SelectItem>
                    <SelectItem value="new_order" className="text-xs">New Order</SelectItem>
                    <SelectItem value="product_deleted" className="text-xs">Product Deleted</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1.5">
                <Label className="text-xs font-medium">Sort by</Label>
                <Select value={sortBy} onValueChange={(value: any) => setSortBy(value)}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date" className="text-xs">Date</SelectItem>
                    <SelectItem value="priority" className="text-xs">Priority</SelectItem>
                    <SelectItem value="type" className="text-xs">Type</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-1.5">
                <Label className="text-xs font-medium">View</Label>
                <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cards" className="text-xs">Cards</SelectItem>
                    <SelectItem value="list" className="text-xs">List</SelectItem>
                    <SelectItem value="grouped" className="text-xs">Grouped</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between pt-3 mt-3 border-t border-gray-100">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  setReadFilter('all')
                  setTypeFilter('all')
                  setSearchQuery('')
                  setSortBy('date')
                  setSortOrder('desc')
                }}
                className="text-gray-600 h-7 px-2 text-xs"
              >
                <RotateCcw className="h-3 w-3 mr-1.5" />
                Reset filters
              </Button>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="flex items-center gap-1.5 h-7 px-2 text-xs"
              >
                {sortOrder === 'desc' ? (
                  <ChevronDown className="h-3 w-3" />
                ) : (
                  <ChevronUp className="h-3 w-3" />
                )}
                {sortOrder === 'desc' ? 'Newest first' : 'Oldest first'}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </>
  )
}