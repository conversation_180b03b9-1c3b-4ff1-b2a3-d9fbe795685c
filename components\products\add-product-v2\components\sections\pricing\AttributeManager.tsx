'use client'

import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Plus, X, Tag, Trash2 } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { useAuth } from '@/contexts/auth-context'
import { designSystem, getInputClasses, getButtonClasses, getBadgeClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'
import { AttributeValueSelector } from './AttributeValueSelector'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'
import { createSupabaseClient } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'

interface AttributeValue {
  id: string
  value: string
}

interface ProductAttribute {
  id: string
  name: string
  values: string[]
  isUserDefined?: boolean
  attributeId?: string // For database-stored attributes
}

export function AttributeManager() {
  const { formData, updateFormData } = useProductForm()
  const { organizationId } = useAuth()
  const { toast } = useToast()
  
  const [attributes, setAttributes] = useState<ProductAttribute[]>([])
  const [availableAttributes, setAvailableAttributes] = useState<Array<{value: string, label: string}>>([])
  const [loadingAttributes, setLoadingAttributes] = useState(false)
  const [newAttributeName, setNewAttributeName] = useState('')
  const [newValues, setNewValues] = useState<{[key: string]: string}>({})
  const [addingValueFor, setAddingValueFor] = useState<string | null>(null)
  const [configuringAttribute, setConfiguringAttribute] = useState<{value: string, label: string} | null>(null)

  // Load available attributes from database
  useEffect(() => {
    if (organizationId) {
      loadAvailableAttributes()
    }
  }, [organizationId])

  const loadAvailableAttributes = async () => {
    if (!organizationId) return
    
    setLoadingAttributes(true)
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('product_attributes')
        .select('id, name')
        .eq('organization_id', organizationId)
        .order('name')

      if (error) throw error
      
      const options = data?.map(attr => ({ value: attr.id, label: attr.name })) || []
      setAvailableAttributes(options)
    } catch (error) {
      console.error('Error loading attributes:', error)
      toast({
        title: "Error",
        description: "Failed to load attributes",
        variant: "destructive"
      })
    } finally {
      setLoadingAttributes(false)
    }
  }

  const createNewAttribute = async (name: string) => {
    if (!organizationId) return null
    
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('product_attributes')
        .insert({
          name: name.trim(),
          organization_id: organizationId
        })
        .select('id, name')
        .single()

      if (error) throw error
      
      // Add to available attributes
      setAvailableAttributes(prev => [...prev, { value: data.id, label: data.name }])
      
      toast({
        title: "Attribute Created",
        description: `Attribute "${name}" created successfully`
      })
      
      return data.id
    } catch (error) {
      console.error('Error creating attribute:', error)
      toast({
        title: "Error",
        description: "Failed to create attribute",
        variant: "destructive"
      })
      return null
    }
  }

  const addExistingAttribute = (attributeId: string) => {
    const attributeOption = availableAttributes.find(attr => attr.value === attributeId)
    if (!attributeOption) return

    // Check if already added
    if (attributes.some(attr => attr.attributeId === attributeId)) {
      toast({
        title: "Info",
        description: "This attribute is already added"
      })
      return
    }

    // Start seamless attribute-value configuration
    setConfiguringAttribute(attributeOption)
  }

  const handleAttributeConfigurationComplete = (attributeName: string, values: string[]) => {
    if (!configuringAttribute) return

    const newAttribute: ProductAttribute = {
      id: `attr_${Date.now()}`,
      name: attributeName,
      values: values,
      isUserDefined: true,
      attributeId: configuringAttribute.value
    }

    setAttributes(prev => [...prev, newAttribute])
    setConfiguringAttribute(null)
  }

  const handleAttributeConfigurationCancel = () => {
    setConfiguringAttribute(null)
  }

  const addCustomAttribute = () => {
    if (!newAttributeName.trim()) return

    const newAttribute: ProductAttribute = {
      id: `attr_${Date.now()}`,
      name: newAttributeName.trim(),
      values: [],
      isUserDefined: false
    }

    setAttributes(prev => [...prev, newAttribute])
    setNewAttributeName('')
  }

  const removeAttribute = (attributeId: string) => {
    setAttributes(prev => prev.filter(attr => attr.id !== attributeId))
    // Remove from new values state
    setNewValues(prev => {
      const updated = { ...prev }
      delete updated[attributeId]
      return updated
    })
  }

  const addValueToAttribute = async (attributeId: string) => {
    const value = newValues[attributeId]?.trim()
    if (!value) return

    const attribute = attributes.find(attr => attr.id === attributeId)
    if (!attribute) return

    // Check for duplicates
    if (attribute.values.includes(value)) {
      toast({
        title: "Info",
        description: "This value already exists"
      })
      return
    }

    // Save to database if it's a user-defined attribute
    if (attribute.attributeId && organizationId) {
      try {
        const supabase = createSupabaseClient()
        const { error } = await supabase
          .from('product_attribute_values')
          .insert({
            attribute_id: attribute.attributeId,
            value: value,
            organization_id: organizationId
          })

        if (error) throw error
      } catch (error) {
        console.error('Error saving attribute value:', error)
        toast({
          title: "Error",
          description: "Failed to save attribute value",
          variant: "destructive"
        })
        return
      }
    }

    // Update local state
    setAttributes(prev => prev.map(attr => 
      attr.id === attributeId 
        ? { ...attr, values: [...attr.values, value] }
        : attr
    ))

    // Clear input and exit add mode
    setNewValues(prev => ({ ...prev, [attributeId]: '' }))
    setAddingValueFor(null)
  }

  const removeValueFromAttribute = (attributeId: string, valueToRemove: string) => {
    setAttributes(prev => prev.map(attr => 
      attr.id === attributeId 
        ? { ...attr, values: attr.values.filter(v => v !== valueToRemove) }
        : attr
    ))
  }

  // Update form data when attributes change
  useEffect(() => {
    const variantAttributes = attributes.map(attr => ({
      name: attr.name,
      values: attr.values
    }))
    updateFormData('variant_attributes', variantAttributes)
  }, [attributes])

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Product Attributes
        </h3>
        <p className={designSystem.typography.caption}>
          Define the attributes that will create your product variations (e.g., Size, Color, Material)
        </p>
      </div>

      {/* Add Attribute Section */}
      <Card className="border-dashed border-2 border-gray-300">
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Add Existing Attribute */}
            <div>
              <FormField label="Add Existing Attribute">
                <SimpleDropdown
                  options={availableAttributes}
                  value=""
                  onValueChange={addExistingAttribute}
                  placeholder="Select an attribute"
                  allowCustom={true}
                  onAddCustomOption={async (option) => {
                    const id = await createNewAttribute(option.label);
                    return id ? { ...option, value: id } : option;
                  }}
                  size="xs"
                  textsize="xs"
                />
              </FormField>
            </div>

            {/* Or Create Custom */}
            <div className="flex items-center gap-2">
              <div className="flex-1 h-px bg-gray-300"></div>
              <span className={`${designSystem.typography.caption} text-gray-500`}>or</span>
              <div className="flex-1 h-px bg-gray-300"></div>
            </div>

            <div className="flex gap-2">
              <Input
                value={newAttributeName}
                onChange={(e) => setNewAttributeName(e.target.value)}
                placeholder="Enter custom attribute name"
                className={getInputClasses()}
                onKeyPress={(e) => e.key === 'Enter' && addCustomAttribute()}
              />
              <Button
                type="button"
                onClick={addCustomAttribute}
                disabled={!newAttributeName.trim()}
                className={getButtonClasses('primary')}
              >
                <Plus className="w-4 h-4" />
                Add
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Seamless Attribute-Value Configuration */}
      {configuringAttribute && (
        <AttributeValueSelector
          attributeOption={configuringAttribute}
          onComplete={handleAttributeConfigurationComplete}
          onCancel={handleAttributeConfigurationCancel}
        />
      )}

      {/* Attributes List */}
      {attributes.map((attribute) => (
        <Card key={attribute.id}>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className={designSystem.typography.subsectionTitle}>
                {attribute.name}
                {attribute.isUserDefined && (
                  <Badge className={`${getBadgeClasses('primary')} ml-2`}>
                    Saved
                  </Badge>
                )}
              </CardTitle>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={() => removeAttribute(attribute.id)}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            {/* Values */}
            <div className="space-y-3">
              {attribute.values.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {attribute.values.map((value, index) => (
                    <Badge
                      key={index}
                      className={`${getBadgeClasses('secondary')} group cursor-pointer`}
                      onClick={() => removeValueFromAttribute(attribute.id, value)}
                    >
                      {value}
                      <X className="w-3 h-3 ml-1 opacity-0 group-hover:opacity-100 transition-opacity" />
                    </Badge>
                  ))}
                </div>
              )}

              {/* Add Value */}
              {addingValueFor === attribute.id ? (
                <div className="flex gap-2">
                  <Input
                    value={newValues[attribute.id] || ''}
                    onChange={(e) => setNewValues(prev => ({ ...prev, [attribute.id]: e.target.value }))}
                    placeholder={`Add ${attribute.name.toLowerCase()} value`}
                    className={getInputClasses()}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        addValueToAttribute(attribute.id)
                      } else if (e.key === 'Escape') {
                        setAddingValueFor(null)
                        setNewValues(prev => ({ ...prev, [attribute.id]: '' }))
                      }
                    }}
                    autoFocus
                  />
                  <Button
                    type="button"
                    onClick={() => addValueToAttribute(attribute.id)}
                    disabled={!newValues[attribute.id]?.trim()}
                    className={getButtonClasses('secondary')}
                  >
                    <Plus className="w-4 h-4" />
                    Add
                  </Button>
                  <Button
                    type="button"
                    onClick={() => {
                      setAddingValueFor(null)
                      setNewValues(prev => ({ ...prev, [attribute.id]: '' }))
                    }}
                    variant="outline"
                    className="px-3"
                  >
                    Cancel
                  </Button>
                </div>
              ) : (
                <Button
                  type="button"
                  onClick={() => setAddingValueFor(attribute.id)}
                  variant="outline"
                  className="w-full justify-center"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add {attribute.name.toLowerCase()} value
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ))}

      {attributes.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Tag className="w-12 h-12 mx-auto mb-4 text-gray-400" />
          <p className={designSystem.typography.body}>
            No attributes added yet
          </p>
          <p className={designSystem.typography.caption}>
            Add attributes to create product variations
          </p>
        </div>
      )}
    </div>
  )
}
