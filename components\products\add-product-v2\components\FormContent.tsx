import React from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { useProductForm } from '../context/ProductFormContext'
import { FORM_SECTIONS, getSectionById, getNextSection, getPreviousSection } from '../config/sections'
import { useAuth } from '@/contexts/auth-context'
import { useToast } from '@/components/ui/use-toast'
import { createSupabaseClient } from '@/lib/supabase'

// Section components (we'll create these next)
import { ProductBasicsSection } from './sections/basics/ProductBasicsSection'
import { PricingVariantsSection } from './sections/pricing/PricingVariantsSection'
import { InventorySettingsSection } from './sections/inventory/InventorySettingsSection'

function SectionHeader() {
  const { currentSection } = useProductForm()
  const section = getSectionById(currentSection)

  return (
    <div className="flex items-center justify-between">
      <div>
        <h2 className="text-lg font-semibold text-gray-900">
          {section?.label || 'Unknown Section'}
        </h2>
        {section?.description && (
          <p className="text-sm text-gray-600 mt-0.5">
            {section.description}
          </p>
        )}
      </div>
      <div className="flex items-center gap-2">
        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
        <span className="text-xs text-gray-500 uppercase tracking-wide">Active</span>
      </div>
    </div>
  )
}

// New Horizontal Navigation Component
function HorizontalNavigation() {
  const { currentSection, setCurrentSection } = useProductForm()

  return (
    <div className="flex items-center gap-6 border-b border-gray-200 pb-3 mb-4">
      {FORM_SECTIONS.map((section) => (
        <button
          key={section.id}
          type="button"
          onClick={() => setCurrentSection(section.id)}
          className={`text-sm font-medium transition-colors duration-200 ${
            currentSection === section.id
              ? 'text-gray-900'
              : 'text-gray-500 hover:text-gray-700'
          }`}
        >
          {section.label}
        </button>
      ))}
    </div>
  )
}

function SectionContent() {
  const { currentSection } = useProductForm()

  switch (currentSection) {
    case 'basics':
      return <ProductBasicsSection />
    case 'pricing':
      return <PricingVariantsSection />
    case 'inventory':
      return <InventorySettingsSection />
    default:
      return (
        <div className="text-center py-12">
          <p className="text-gray-500">Section not found</p>
        </div>
      )
  }
}

function ActionButtons() {
  const { formData, resetForm, isSubmitting, setIsSubmitting, savedVariants } = useProductForm()
  const { user, organizationId } = useAuth()
  const { toast } = useToast()
  const supabase = createSupabaseClient()

  const handleSave = async () => {
    if (!user || !organizationId) {
      toast({
        title: "Authentication Error",
        description: "Please sign in to create products.",
        variant: "destructive",
      })
      return
    }

    // Validate required fields
    if (!formData.name || !formData.base_sku) {
      toast({
        title: "Missing Information",
        description: "Product name and SKU are required.",
        variant: "destructive",
      })
      return
    }

    // Validate simple product pricing
    if (!formData.has_variants && !formData.price) {
      toast({
        title: "Missing Pricing",
        description: "Simple products require a selling price.",
        variant: "destructive",
      })
      return
    }

    // Validate variable product attributes
    if (formData.has_variants && formData.variant_attributes.length === 0) {
      toast({
        title: "Missing Attributes",
        description: "Variable products require at least one attribute.",
        variant: "destructive",
      })
      return
    }

    // Validate variable product variants
    if (formData.has_variants && savedVariants.length === 0) {
      toast({
        title: "Missing Variants",
        description: "Please generate and configure variants for your variable product.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // Prepare product data
      const productPayload = {
        organization_id: organizationId,
        name: formData.name,
        description: formData.description || null,
        brand: formData.brand || null,
        supplier: formData.supplier || null, // Use supplier ID instead of name
        base_sku: formData.base_sku,
        category_id: formData.category_id || null,
        has_variants: formData.has_variants,
        track_inventory: true,
        base_cost: formData.base_cost || 0,
        packaging_cost: formData.packaging_cost || 0,
        price: formData.price,
        size: formData.size || null,
        color: formData.color || null,
        stock_quantity: formData.stock_quantity || 0,
        low_stock_threshold: formData.low_stock_threshold || 10,
        barcode: formData.barcode || null,
        sale_price: formData.sale_price || null,
        sale_start_date: formData.sale_start_date ? formData.sale_start_date.toISOString().split('T')[0] : null,
        sale_end_date: formData.sale_end_date ? formData.sale_end_date.toISOString().split('T')[0] : null,
        batch_reference: formData.batch_reference || null,
        purchase_date: formData.purchase_date ? formData.purchase_date.toISOString().split('T')[0] : null,
        notes: formData.notes || null,
      }

      // Create the product
      const { data: productData, error: productError } = await supabase
        .from('products')
        .insert(productPayload)
        .select()
        .single()

      if (productError) {
        console.error('Error creating product:', productError)
        toast({
          title: "Error",
          description: `Failed to create product: ${productError.message}`,
          variant: "destructive",
        })
        return
      }

      // Create variants if this is a variable product
      if (formData.has_variants && savedVariants.length > 0) {
        const variantData = savedVariants.map(variant => ({
          product_id: productData.id,
          organization_id: organizationId,
          sku: variant.sku,
          price: variant.price,
          base_cost: variant.base_cost,
          packaging_cost: variant.packaging_cost,
          stock_quantity: variant.quantity,
          low_stock_threshold: variant.low_stock_threshold,
          attributes: variant.attributes.reduce((acc, attr) => {
            acc[attr.name] = attr.value
            return acc
          }, {} as Record<string, string>)
        }))

        const { error: variantError } = await supabase
          .from('product_variants')
          .insert(variantData)

        if (variantError) {
          console.error('Error creating variants:', variantError)
          toast({
            title: "Warning",
            description: "Product created but some variants failed to save. Please check and add them manually.",
            variant: "destructive",
          })
        }
      }

      console.log('Product created successfully:', productData)
      toast({
        title: "Success",
        description: `Product "${formData.name}" created successfully!`,
      })

      // Reset form after successful creation
      resetForm()
    } catch (error) {
      console.error('Unexpected error creating product:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="flex items-center justify-end gap-2">
      <Button
        type="button"
        variant="outline"
        onClick={resetForm}
        disabled={isSubmitting}
        className="h-8 px-3 text-xs"
      >
        Reset
      </Button>

      <Button
        type="button"
        onClick={handleSave}
        disabled={isSubmitting || !formData.name || !formData.base_sku}
        className="h-8 px-3 text-xs bg-green-600 hover:bg-green-700"
      >
        {isSubmitting ? 'Creating...' : 'Create Product'}
      </Button>
    </div>
  )
}

export function FormContent() {
  return (
    <div className="flex-1 flex flex-col bg-white overflow-hidden">
      {/* Top divider aligned with left column - adjusted padding for proper alignment */}
      <div className="px-6 pt-9 pb-3 border-b border-gray-200 bg-white flex-shrink-0 pr-12">
        <div className="h-0"></div>
      </div>

      {/* Scrollable content */}
      <div className="flex-1 overflow-y-auto px-6 py-4">
        {/* Horizontal Navigation */}
        <HorizontalNavigation />
        
        {/* Section Content */}
        <SectionContent />
      </div>

      {/* Action buttons footer */}
      <div className="border-t border-gray-200 px-6 py-3 bg-gray-50 flex-shrink-0">
        <ActionButtons />
      </div>
    </div>
  )
}