'use client'

import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Calculator, TrendingUp, Package, ChevronDown, ChevronRight } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses, getBadgeClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

export function SimplePricingForm() {
  const { formData, updateFormData, errors } = useProductForm()
  const [isSalePricingExpanded, setIsSalePricingExpanded] = useState(false)

  // Calculate profit margin
  const calculateProfitMargin = () => {
    const totalCost = (formData.base_cost || 0) + (formData.packaging_cost || 0)
    const price = formData.price || 0
    if (totalCost === 0 || price === 0) return 0
    return ((price - totalCost) / price * 100)
  }

  // Calculate total cost and percentages for the proportional bar
  const baseCost = formData.base_cost || 0;
  const packagingCost = formData.packaging_cost || 0;
  const totalCost = baseCost + packagingCost;
  const sellingPrice = formData.price || 0;
  
  // Calculate percentages for the visualization bar
  const maxDisplayValue = Math.max(totalCost, sellingPrice, 1); // Use 1 as minimum to avoid division by zero
  const unitCostPercentage = (baseCost / maxDisplayValue) * 100;
  const packagingCostPercentage = (packagingCost / maxDisplayValue) * 100;
  const sellingPricePercentage = (sellingPrice / maxDisplayValue) * 100;
  
  // Calculate profit (only shown in the bar if selling price > total cost)
  const profit = sellingPrice - totalCost;
  const profitPercentage = profit > 0 ? (profit / maxDisplayValue) * 100 : 0;

  const profitMargin = calculateProfitMargin()

  // Get margin color based on percentage
  const getMarginColor = (margin: number) => {
    if (margin >= 30) return 'success'
    if (margin >= 15) return 'warning'
    return 'error'
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Simple Product Pricing
        </h3>
        <p className={designSystem.typography.caption}>
          Set pricing for your simple product. All calculations are done automatically.
        </p>
      </div>

      {/* Cost Structure and Selling Price - Same Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="md:col-span-2">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormField
              label="Unit Cost (excluding packaging)"
              required
              error={errors.base_cost}
            >
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                <Input
                  type="number"
                  value={formData.base_cost || ''}
                  onChange={(e) => updateFormData('base_cost', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className={`${getInputClasses(!!errors.base_cost)} pl-6`}
                  step="0.01"
                  min="0"
                />
              </div>
            </FormField>

            <FormField
              label="Packaging Cost"
              error={errors.packaging_cost}
            >
              <div className="relative">
                <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                <Input
                  type="number"
                  value={formData.packaging_cost || ''}
                  onChange={(e) => updateFormData('packaging_cost', parseFloat(e.target.value) || 0)}
                  placeholder="0.00"
                  className={`${getInputClasses(!!errors.packaging_cost)} pl-6`}
                  step="0.01"
                  min="0"
                />
              </div>
            </FormField>
          </div>
        </div>

        <div className="md:col-span-1">
          <FormField
            label="Selling Price"
            required
            error={errors.price}
          >
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
              <Input
                type="number"
                value={formData.price || ''}
                onChange={(e) => updateFormData('price', parseFloat(e.target.value) || null)}
                placeholder="0.00"
                className={`${getInputClasses(!!errors.price)} pl-6`}
                step="0.01"
                min="0"
              />
            </div>
          </FormField>
        </div>
      </div>

      {/* Profit Margin Visualization */}
      <div className="bg-white/30 backdrop-blur-2xl rounded-lg border border-white/20 shadow-lg p-5">
        <div className="flex justify-between items-baseline mb-3">
          <h3 className="text-sm font-medium text-slate-800">Profit Margin</h3>
          <p className="text-xl font-bold text-slate-900">{profitMargin.toFixed(1)}%</p>
        </div>
        <div 
          className="w-full flex rounded-full h-2.5 bg-black/5 shadow-md border border-white/50 overflow-hidden"
          title={`Unit: ${baseCost.toFixed(2)}, Packaging: ${packagingCost.toFixed(2)}, Profit: ${profit > 0 ? profit.toFixed(2) : '0.00'}`}
        >
          <div 
            className="bg-slate-800 transition-all duration-300" 
            style={{ width: `${unitCostPercentage}%` }}
          ></div>
          <div 
            className="bg-slate-400 transition-all duration-300" 
            style={{ width: `${packagingCostPercentage}%` }}
          ></div>
          {profit > 0 ? (
            <div 
              className="bg-emerald-400 transition-all duration-300" 
              style={{ width: `${profitPercentage}%` }}
            ></div>
          ) : (
            <div 
              className="bg-rose-400 transition-all duration-300" 
              style={{ width: `${Math.max(0, sellingPricePercentage - unitCostPercentage - packagingCostPercentage)}%` }}
            ></div>
          )}
        </div>
        <div className="mt-4 flex justify-between text-xs">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-slate-800"></div>
            <span className="text-slate-700">Unit Cost: <span className="font-medium text-slate-900">${baseCost.toFixed(2)}</span></span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 rounded-full bg-slate-400"></div>
            <span className="text-slate-700">Packaging: <span className="font-medium text-slate-900">${packagingCost.toFixed(2)}</span></span>
          </div>
          <div className="flex items-center gap-2">
            {profit > 0 ? (
              <>
                <div className="w-2 h-2 rounded-full bg-emerald-400"></div>
                <span className="text-slate-700">Profit: <span className="font-medium text-slate-900">${profit.toFixed(2)}</span></span>
              </>
            ) : (
              <>
                <div className="w-2 h-2 rounded-full bg-rose-400"></div>
                <span className="text-slate-700">Loss</span>
              </>
            )}
          </div>
        </div>
      </div>

      {/* Expandable Sale Pricing Section */}
      <div className="border border-gray-200 rounded-lg">
        <button
          type="button"
          className="w-full flex items-center justify-between p-4 text-left"
          onClick={() => setIsSalePricingExpanded(!isSalePricingExpanded)}
        >
          <div>
            <h4 className={`${designSystem.typography.label} text-gray-700`}>
              Sale Pricing (Optional)
            </h4>
            <p className={`${designSystem.typography.caption} text-gray-500 mt-1`}>
              Set special pricing for sales or promotions
            </p>
          </div>
          {isSalePricingExpanded ? (
            <ChevronDown className="h-5 w-5 text-gray-500" />
          ) : (
            <ChevronRight className="h-5 w-5 text-gray-500" />
          )}
        </button>
        
        {isSalePricingExpanded && (
          <div className="border-t border-gray-200 p-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FormField
                label="Sale Price"
                error={errors.sale_price}
              >
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                  <Input
                    type="number"
                    value={formData.sale_price || ''}
                    onChange={(e) => updateFormData('sale_price', parseFloat(e.target.value) || null)}
                    placeholder="0.00"
                    className={`${getInputClasses(!!errors.sale_price)} pl-6`}
                    step="0.01"
                    min="0"
                  />
                </div>
              </FormField>

              <FormField
                label="Sale Start Date"
                error={errors.sale_start_date}
              >
                <Input
                  type="date"
                  value={formData.sale_start_date ? formData.sale_start_date.toISOString().split('T')[0] : ''}
                  onChange={(e) => updateFormData('sale_start_date', e.target.value ? new Date(e.target.value) : null)}
                  className={getInputClasses(!!errors.sale_start_date)}
                />
              </FormField>

              <FormField
                label="Sale End Date"
                error={errors.sale_end_date}
              >
                <Input
                  type="date"
                  value={formData.sale_end_date ? formData.sale_end_date.toISOString().split('T')[0] : ''}
                  onChange={(e) => updateFormData('sale_end_date', e.target.value ? new Date(e.target.value) : null)}
                  className={getInputClasses(!!errors.sale_end_date)}
                />
              </FormField>
            </div>

            {formData.sale_price && formData.price && (
              <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-md">
                <p className={`${designSystem.typography.caption} text-green-700`}>
                  <strong>Sale Discount:</strong> {(((formData.price - formData.sale_price) / formData.price) * 100).toFixed(1)}% off
                  (Save ${(formData.price - formData.sale_price).toFixed(2)})
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  )
}