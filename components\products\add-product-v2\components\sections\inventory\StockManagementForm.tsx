'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Switch } from '@/components/ui/switch'
import { Badge } from '@/components/ui/badge'
import { AlertTriangle, Package, TrendingDown } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses, getBadgeClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

export function StockManagementForm() {
  const { formData, updateFormData, errors } = useProductForm()

  // Only show for simple products (variable products manage stock per variant)
  if (formData.has_variants) {
    return (
      <div className="text-center py-8">
        <Package className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className={designSystem.typography.body}>
          Stock Management for Variable Products
        </p>
        <p className={designSystem.typography.caption}>
          Stock is managed individually for each variant in the Pricing & Variants section
        </p>
      </div>
    )
  }

  const getStockStatus = () => {
    const stock = formData.stock_quantity || 0
    const threshold = formData.low_stock_threshold || 10
    
    if (stock === 0) return { status: 'out', color: 'error' as const, label: 'Out of Stock' }
    if (stock <= threshold) return { status: 'low', color: 'warning' as const, label: 'Low Stock' }
    return { status: 'good', color: 'success' as const, label: 'In Stock' }
  }

  const stockStatus = getStockStatus()

  return (
    <div className="space-y-6">
      {/* Stock Status Overview */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <div className="flex items-center justify-between mb-3">
          <h4 className={`${designSystem.typography.label} text-gray-700`}>
            Current Stock Status
          </h4>
          <Badge className={getBadgeClasses(stockStatus.color)}>
            {stockStatus.label}
          </Badge>
        </div>
        
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <p className={designSystem.typography.caption}>Current Stock</p>
            <p className="font-bold text-2xl text-gray-900">{formData.stock_quantity || 0}</p>
          </div>
          <div>
            <p className={designSystem.typography.caption}>Low Stock Alert</p>
            <p className="font-bold text-2xl text-gray-900">{formData.low_stock_threshold || 10}</p>
          </div>
          <div>
            <p className={designSystem.typography.caption}>Available</p>
            <p className="font-bold text-2xl text-gray-900">{Math.max(0, formData.stock_quantity || 0)}</p>
          </div>
        </div>
      </div>

      {/* Stock Configuration */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Stock Quantity"
          required
          error={errors.stock_quantity}
        >
          <Input
            type="number"
            value={formData.stock_quantity !== undefined ? formData.stock_quantity : ''}
            onChange={(e) => {
              const value = e.target.value
              if (value === '') {
                updateFormData('stock_quantity', undefined)
              } else {
                const numValue = parseInt(value)
                updateFormData('stock_quantity', isNaN(numValue) ? undefined : numValue)
              }
            }}
            placeholder="Enter stock quantity"
            className={getInputClasses(!!errors.stock_quantity)}
            min="0"
          />
        </FormField>

        <FormField
          label="Low Stock Threshold"
          error={errors.low_stock_threshold}
        >
          <Input
            type="number"
            value={formData.low_stock_threshold || ''}
            onChange={(e) => updateFormData('low_stock_threshold', parseInt(e.target.value) || 10)}
            placeholder="10"
            className={getInputClasses(!!errors.low_stock_threshold)}
            min="0"
          />
        </FormField>
      </div>

      {/* Stock Alerts */}
      {stockStatus.status === 'out' && (
        <div className="flex items-start gap-3 p-4 bg-red-50 border border-red-200 rounded-lg">
          <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className={`${designSystem.typography.label} text-red-700`}>
              Out of Stock
            </p>
            <p className={`${designSystem.typography.caption} text-red-600`}>
              This product is currently out of stock and won't be available for sale.
            </p>
          </div>
        </div>
      )}

      {stockStatus.status === 'low' && (
        <div className="flex items-start gap-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <TrendingDown className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
          <div>
            <p className={`${designSystem.typography.label} text-yellow-700`}>
              Low Stock Warning
            </p>
            <p className={`${designSystem.typography.caption} text-yellow-600`}>
              Stock level is below the threshold. Consider restocking soon.
            </p>
          </div>
        </div>
      )}

      {/* Additional Stock Fields */}
      <div className="border-t border-gray-200 pt-6">
        <h4 className={`${designSystem.typography.label} text-gray-700 mb-4`}>
          Additional Information
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Barcode"
            error={errors.barcode}
          >
            <Input
              value={formData.barcode || ''}
              onChange={(e) => updateFormData('barcode', e.target.value)}
              placeholder="Enter barcode"
              className={getInputClasses(!!errors.barcode)}
            />
          </FormField>

          <FormField
            label="Batch Reference"
            error={errors.batch_reference}
          >
            <Input
              value={formData.batch_reference || ''}
              onChange={(e) => updateFormData('batch_reference', e.target.value)}
              placeholder="Batch/Lot number"
              className={getInputClasses(!!errors.batch_reference)}
            />
          </FormField>
        </div>

        <div className="mt-4">
          <FormField
            label="Purchase Date"
            error={errors.purchase_date}
          >
            <Input
              type="date"
              value={formData.purchase_date ? formData.purchase_date.toISOString().split('T')[0] : ''}
              onChange={(e) => updateFormData('purchase_date', e.target.value ? new Date(e.target.value) : null)}
              className={getInputClasses(!!errors.purchase_date)}
            />
          </FormField>
        </div>
      </div>
    </div>
  )
}