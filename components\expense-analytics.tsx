'use client'

import { useState, useEffect, useMemo, useCallback } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { MetricCard } from '@/components/ui/metric-card'
import { 
  CalendarDays, 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Download,
  Calendar,
  Filter,
  FileSpreadsheet,
  FileText,
  ChevronDown,
  Wallet,
  Pie<PERSON>hart as PieChartIcon
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  PieController,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js'
import { Bar, Line, Pie } from 'react-chartjs-2'
import { type ExpenseRow } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'
import { useToast } from '@/components/ui/use-toast'
import { cn } from '@/lib/utils'
import { 
  type PeriodType,
  getTrendChartTitle,
  getCategoryBreakdownTitle
} from '@/lib/period-calculations'
import { 
  processAnalyticsData,
  getPrimaryData,
  generateQuickInsights,
  type AnalyticsData,
  type PeriodData,
  type CategoryData
} from '@/lib/analytics-processor'
import { PageHeader } from '@/components/ui/page-header'

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  PieController,
  ArcElement,
  Title,
  Tooltip,
  Legend,
  Filler
)

// Period selection options
const PERIOD_OPTIONS = [
  { value: 'month' as const, label: 'This Month', icon: CalendarDays },
  { value: 'quarter' as const, label: 'This Quarter', icon: BarChart3 },
  { value: 'year' as const, label: 'This Year', icon: Calendar },
]

interface ExpenseAnalyticsProps {
  expenses?: ExpenseRow[]
  onPeriodChange?: (period: PeriodType) => void
  isCollapsible?: boolean
}

export function ExpenseAnalytics({ 
  expenses = [], 
  onPeriodChange,
  isCollapsible = true 
}: ExpenseAnalyticsProps) {
  const [selectedPeriod, setSelectedPeriod] = useState<PeriodType>('month')
  const [isExpanded, setIsExpanded] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  
  const { user } = useAuth()
  const { formatCurrency } = useCurrency()
  const { toast } = useToast()

  // Process analytics data using modular utilities
  const analyticsData = useMemo(() => {
    return processAnalyticsData(expenses, selectedPeriod)
  }, [expenses, selectedPeriod])

  // Get primary data for the selected period
  const primaryData = useMemo(() => {
    return getPrimaryData(analyticsData, selectedPeriod)
  }, [analyticsData, selectedPeriod])

  // Generate contextual insights
  const quickInsights = useMemo(() => {
    return generateQuickInsights(analyticsData, selectedPeriod)
  }, [analyticsData, selectedPeriod])

  const handlePeriodChange = (period: PeriodType) => {
    setSelectedPeriod(period)
    onPeriodChange?.(period)
  }

  // Export functions for analytics data
  const exportAnalyticsToCSV = useCallback(() => {
    if (!analyticsData) return
    
    setIsExporting(true)
    
    try {
      const currentDate = new Date().toISOString().split('T')[0]
      const periodLabel = selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)
      
      // Create comprehensive analytics CSV
      const lines = [
        '# Expense Analytics Report',
        `# Generated on: ${new Date().toISOString()}`,
        `# Period Focus: ${periodLabel}`,
        `# Primary Period: ${primaryData?.period}`,
        '#',
        '# Summary Data:',
        'Period,Amount,Count,Change %',
        `"${analyticsData.currentPeriod.period}",${analyticsData.currentPeriod.amount.toFixed(2)},${analyticsData.currentPeriod.count},${analyticsData.currentPeriod.change?.toFixed(2) || 'N/A'}`,
        `"${analyticsData.previousPeriod.period}",${analyticsData.previousPeriod.amount.toFixed(2)},${analyticsData.previousPeriod.count},`,
        `"${analyticsData.currentQuarter.period}",${analyticsData.currentQuarter.amount.toFixed(2)},${analyticsData.currentQuarter.count},`,
        `"${analyticsData.currentYear.period}",${analyticsData.currentYear.amount.toFixed(2)},${analyticsData.currentYear.count},`,
        '#',
        '# Category Breakdown:',
        'Category,Amount,Count,Percentage',
        ...analyticsData.topCategories.map(cat => 
          `"${cat.category}",${cat.amount.toFixed(2)},${cat.count},${cat.percentage.toFixed(2)}%`
        ),
        '#',
        `# Trend Data (${getTrendChartTitle(selectedPeriod)}):`,
        'Period,Amount,Count',
        ...analyticsData.trendData.map(trend => 
          `"${trend.period}",${trend.amount.toFixed(2)},${trend.count}`
        )
      ]
      
      const csvContent = lines.join('\n')
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `expense-analytics-${periodLabel.toLowerCase()}-${currentDate}.csv`
      link.style.display = 'none'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast({
        title: "Analytics Export Complete",
        description: `${periodLabel} analytics report has been downloaded as CSV.`,
      })
    } catch (error) {
      console.error('Analytics CSV export error:', error)
      toast({
        title: "Export Failed",
        description: "Failed to export analytics. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsExporting(false)
    }
  }, [analyticsData, selectedPeriod, primaryData, toast])
  
  const exportAnalyticsToPDF = useCallback(() => {
    if (!analyticsData) return
    
    setIsExporting(true)
    
    try {
      // Dynamic import to avoid SSR issues
      import('jspdf').then(({ default: jsPDF }) => {
        import('jspdf-autotable').then(() => {
          try {
            const doc = new jsPDF()
            const currentDate = new Date().toLocaleDateString()
            const periodLabel = selectedPeriod.charAt(0).toUpperCase() + selectedPeriod.slice(1)
            
            // Set document properties
            doc.setProperties({
              title: `Expense Analytics Report - ${periodLabel}`,
              subject: 'Business Expense Analytics',
              creator: 'ONKO Expense Management'
            })
            
            // Add header
            doc.setFontSize(20)
            doc.text('Expense Analytics Report', 20, 25)
            
            doc.setFontSize(12)
            doc.text(`Generated on: ${currentDate}`, 20, 35)
            doc.text(`Period Focus: ${periodLabel}`, 20, 45)
            doc.text(`Primary Period: ${primaryData?.period}`, 20, 55)
            
            // Summary section
            doc.setFontSize(14)
            doc.text('Summary', 20, 70)
            
            const summaryData = [
              [analyticsData.currentPeriod.period, formatCurrency(analyticsData.currentPeriod.amount), analyticsData.currentPeriod.count.toString(), analyticsData.currentPeriod.change ? `${analyticsData.currentPeriod.change.toFixed(1)}%` : 'N/A'],
              [analyticsData.previousPeriod.period, formatCurrency(analyticsData.previousPeriod.amount), analyticsData.previousPeriod.count.toString(), '-'],
              [analyticsData.currentQuarter.period, formatCurrency(analyticsData.currentQuarter.amount), analyticsData.currentQuarter.count.toString(), '-'],
              [analyticsData.currentYear.period, formatCurrency(analyticsData.currentYear.amount), analyticsData.currentYear.count.toString(), '-']
            ]
            
            ;(doc as any).autoTable({
              head: [['Period', 'Amount', 'Count', 'Change %']],
              body: summaryData,
              startY: 75,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Category breakdown
            const finalY = (doc as any).lastAutoTable.finalY || 75
            doc.setFontSize(14)
            doc.text('Top Categories', 20, finalY + 20)
            
            const categoryData = analyticsData.topCategories.map(cat => [
              cat.category,
              formatCurrency(cat.amount),
              cat.count.toString(),
              `${cat.percentage.toFixed(1)}%`
            ])
            
            ;(doc as any).autoTable({
              head: [['Category', 'Amount', 'Count', 'Percentage']],
              body: categoryData,
              startY: finalY + 25,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Trend data
            const finalY2 = (doc as any).lastAutoTable.finalY || finalY + 25
            doc.setFontSize(14)
            doc.text(getTrendChartTitle(selectedPeriod), 20, finalY2 + 20)
            
            const trendDataForPDF = analyticsData.trendData.map(trend => [
              trend.period,
              formatCurrency(trend.amount),
              trend.count.toString()
            ])
            
            ;(doc as any).autoTable({
              head: [['Period', 'Amount', 'Count']],
              body: trendDataForPDF,
              startY: finalY2 + 25,
              styles: { fontSize: 10 },
              headStyles: { fillColor: [59, 130, 246] }
            })
            
            // Save PDF
            doc.save(`expense-analytics-${periodLabel.toLowerCase()}-${new Date().toISOString().split('T')[0]}.pdf`)
            
            toast({
              title: "Analytics PDF Export Complete",
              description: `${periodLabel} analytics report has been downloaded as PDF.`,
            })
          } catch (error) {
            console.error('Analytics PDF export error:', error)
            toast({
              title: "PDF Export Failed",
              description: "Failed to generate analytics PDF. Please try again.",
              variant: "destructive",
            })
          } finally {
            setIsExporting(false)
          }
        })
      })
    } catch (error) {
      console.error('PDF library loading error:', error)
      toast({
        title: "PDF Export Failed",
        description: "Failed to load PDF library. Please try again.",
        variant: "destructive",
      })
      setIsExporting(false)
    }
  }, [analyticsData, selectedPeriod, primaryData, formatCurrency, toast])

  // Generate chart data for pie chart
  const pieChartData = useMemo(() => {
    if (!analyticsData || analyticsData.topCategories.length === 0) {
      return {
        labels: [],
        datasets: []
      }
    }

    return {
      labels: analyticsData.topCategories.map(cat => cat.category),
      datasets: [
        {
          data: analyticsData.topCategories.map(cat => cat.amount),
          backgroundColor: analyticsData.topCategories.map(cat => cat.color),
          borderWidth: 1
        }
      ]
    }
  }, [analyticsData])

  // Generate chart data for line chart
  const lineChartData = useMemo(() => {
    if (!analyticsData || analyticsData.trendData.length === 0) {
      return {
        labels: [],
        datasets: []
      }
    }

    return {
      labels: analyticsData.trendData.map(trend => trend.period),
      datasets: [
        {
          label: 'Amount',
          data: analyticsData.trendData.map(trend => trend.amount),
          borderColor: '#3B82F6',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.4
        }
      ]
    }
  }, [analyticsData])

  // Chart options for pie chart
  const pieChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'right' as const,
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        callbacks: {
          label: function(context: any) {
            const label = context.label || ''
            const value = context.raw || 0
            const percentage = analyticsData?.topCategories.find(cat => cat.category === label)?.percentage || 0
            return `${label}: ${formatCurrency(value)} (${percentage.toFixed(1)}%)`
          }
        }
      }
    }
  }

  // Chart options for line chart
  const lineChartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20
        }
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        callbacks: {
          label: function(context: any) {
            return `${context.dataset.label}: ${formatCurrency(context.parsed.y)}`
          }
        }
      }
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#94a3b8'
        }
      },
      y: {
        grid: {
          color: '#f1f5f9'
        },
        ticks: {
          color: '#94a3b8',
          callback: function(value: any) {
            return formatCurrency(value)
          }
        }
      }
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false
    }
  }

  if (!analyticsData) {
    const periodLabel = PERIOD_OPTIONS.find(option => option.value === selectedPeriod)?.label || 'Selected Period'
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center space-y-2">
            <div className="text-muted-foreground">Loading {periodLabel.toLowerCase()} analytics...</div>
            <div className="text-xs text-muted-foreground">Processing expense data and calculations</div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex items-center justify-between">
        {/* Removed duplicate PageHeader component to prevent title duplication */}
        
        <div className="flex items-center space-x-2">
          {/* Period Selection */}
          <div className="flex items-center space-x-1 bg-muted rounded-lg p-1">
            {PERIOD_OPTIONS.map((option) => {
              const Icon = option.icon
              return (
                <Button
                  key={option.value}
                  variant={selectedPeriod === option.value ? "default" : "ghost"}
                  size="sm"
                  onClick={() => handlePeriodChange(option.value)}
                  className="relative"
                >
                  <Icon className="h-4 w-4 mr-1" />
                  {option.label}
                </Button>
              )
            })}
          </div>
          
          {/* Export Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                disabled={isExporting || !analyticsData || expenses.length === 0}
                className="flex items-center space-x-1"
              >
                <Download className="h-4 w-4" />
                <span>Export</span>
                <ChevronDown className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={exportAnalyticsToCSV} disabled={isExporting}>
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Export as CSV
              </DropdownMenuItem>
              <DropdownMenuItem onClick={exportAnalyticsToPDF} disabled={isExporting}>
                <FileText className="h-4 w-4 mr-2" />
                Export as PDF
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          
          {/* Collapse Toggle */}
          {isCollapsible && (
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <Filter className={cn("h-4 w-4 transition-transform", isExpanded ? "rotate-180" : "")} />
            </Button>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-12 gap-6">
        {/* Primary Period Card */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title={primaryData?.period || "Current Period"}
            value={formatCurrency(primaryData?.amount || 0)}
            subtitle={`${primaryData?.count || 0} expenses`}
            icon={<Wallet className="h-4 w-4 text-primary" />}
          />
        </div>

        {/* Previous Period Comparison */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title={analyticsData.previousPeriod.period}
            value={formatCurrency(analyticsData.previousPeriod.amount)}
            subtitle={`${analyticsData.previousPeriod.count} expenses`}
            icon={<Calendar className="h-4 w-4 text-primary" />}
          />
        </div>

        {/* Current Quarter */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="This Quarter"
            value={formatCurrency(analyticsData.currentQuarter.amount)}
            subtitle={`${analyticsData.currentQuarter.count} expenses`}
            icon={<BarChart3 className="h-4 w-4 text-primary" />}
          />
        </div>

        {/* Current Year */}
        <div className="col-span-12 md:col-span-3">
          <MetricCard
            title="This Year"
            value={formatCurrency(analyticsData.currentYear.amount)}
            subtitle={`${analyticsData.currentYear.count} expenses`}
            icon={<CalendarDays className="h-4 w-4 text-primary" />}
          />
        </div>
      </div>

      {/* Expandable Detailed Analytics */}
      {isExpanded && (
        <div className="space-y-6">
          {/* Category Breakdown with Chart */}
          <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
            <CardHeader>
              <CardTitle className="text-lg">{getCategoryBreakdownTitle(selectedPeriod)}</CardTitle>
              <CardDescription>
                Visual breakdown of spending by category
              </CardDescription>
            </CardHeader>
            <CardContent>
              {analyticsData.topCategories.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <div>No expenses recorded for {primaryData?.period.toLowerCase()}</div>
                  <div className="text-xs mt-1">Add some expenses to see category breakdown</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Pie Chart */}
                  <div className="h-64">
                    <Pie data={pieChartData} options={pieChartOptions} />
                  </div>
                  
                  {/* Category List */}
                  <div className="space-y-3">
                    {analyticsData.topCategories.map((category, index) => (
                      <div key={category.category} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: category.color }}
                          />
                          <span className="font-medium">{category.category}</span>
                        </div>
                        <div className="text-right">
                          <div className="font-semibold">{formatCurrency(category.amount)}</div>
                          <div className="text-xs text-muted-foreground">
                            {category.count} expenses ({category.percentage.toFixed(1)}%)
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Charts Row */}
          <div className="grid grid-cols-12 gap-6">
            {/* Interactive Trend Chart */}
            <div className="col-span-12 lg:col-span-8">
              <Card className="shadow-md border border-gray-200 bg-white rounded-lg">
                <CardHeader>
                  <CardTitle className="text-lg">{getTrendChartTitle(selectedPeriod)}</CardTitle>
                  <CardDescription>
                    Historical expense patterns and trends
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  {analyticsData.trendData.length === 0 ? (
                    <div className="text-center py-8 text-muted-foreground">
                      <div>No trend data available</div>
                      <div className="text-xs mt-1">Add expenses across multiple periods to see trends</div>
                    </div>
                  ) : (
                    <div className="h-64">
                      <Line data={lineChartData} options={lineChartOptions} />
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            
            {/* Quick Insights Card */}
            <div className="col-span-12 lg:col-span-4">
              <Card className="shadow-md border border-gray-200 bg-white rounded-lg h-full">
                <CardHeader>
                  <CardTitle className="text-lg">Quick Insights - {primaryData?.period}</CardTitle>
                  <CardDescription>
                    Key statistics and trends for selected period
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {quickInsights.map((insight, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm text-muted-foreground">{insight.label}</span>
                        <span className={cn(
                          "font-medium",
                          insight.trend === 'up' && "text-red-600",
                          insight.trend === 'down' && "text-green-600",
                          insight.trend === 'neutral' && "text-muted-foreground"
                        )}>
                          {insight.value}
                        </span>
                      </div>
                    ))}
                    {quickInsights.length === 0 && (
                      <div className="text-center py-4 text-muted-foreground">
                        <div>No insights available for {primaryData?.period.toLowerCase()}</div>
                        <div className="text-xs mt-1">Add expenses to generate insights</div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}