'use client'

import React from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { FileText, Palette, Ruler } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

export function AdditionalSettingsForm() {
  const { formData, updateFormData, errors } = useProductForm()

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Additional Settings
        </h3>
        <p className={designSystem.typography.caption}>
          Optional fields for additional product information and metadata
        </p>
      </div>



      {/* Notes Section */}
      <div className="space-y-4">
        <h4 className={`${designSystem.typography.label} text-gray-700 flex items-center gap-2`}>
          <FileText className="w-4 h-4" />
          Notes & Documentation
        </h4>
        
        <FormField
          label="Internal Notes"
          error={errors.notes}
        >
          <Textarea
            value={formData.notes || ''}
            onChange={(e) => updateFormData('notes', e.target.value)}
            placeholder="Add any internal notes, special handling instructions, or additional information..."
            rows={4}
            className={`${getInputClasses(!!errors.notes)} resize-none`}
          />
        </FormField>
      </div>


    </div>
  )
}
