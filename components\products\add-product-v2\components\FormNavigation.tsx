import React from 'react'
import { AlertCircle, ChevronRight, Check, Package, DollarSign, Layers } from 'lucide-react'
import { cn } from '@/lib/utils'
import { useProductForm } from '../context/ProductFormContext'
import { FORM_SECTIONS } from '../config/sections'

interface NavigationItemProps {
  section: typeof FORM_SECTIONS[0]
  isActive: boolean
  isCompleted: boolean
  hasErrors: boolean
  onClick: () => void
}

export function FormNavigation() {
  const { currentSection, setCurrentSection, errors, isValid } = useProductForm()
  
  // Calculate progress based on completed sections
  const completedSections = FORM_SECTIONS.filter(section => isValid[section.id]).length
  const progress = (completedSections / FORM_SECTIONS.length) * 100
  
  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-[#172941] to-[#111427]">
      {/* Header */}
      <div className="px-6 py-3 border-b border-white/10">
        <h1 className="text-base font-semibold text-white">
          Add New Product
        </h1>
      </div>

      {/* Navigation sections */}
      <nav className="px-6 py-4 space-y-1">
        {FORM_SECTIONS.map((section) => (
          <NavigationItem
            key={section.id}
            section={section}
            isActive={currentSection === section.id}
            isCompleted={isValid[section.id] || false}
            hasErrors={!!errors[section.id]}
            onClick={() => setCurrentSection(section.id)}
          />
        ))}
      </nav>

      {/* Product Summary - Moved to be directly below navigation */}
      <div className="px-6 pb-4">
        <ProductSummary />
      </div>

      {/* Spacer to push content to top */}
      <div className="flex-1"></div>

    </div>
  )
}

function NavigationItem({ section, isActive, isCompleted, hasErrors, onClick }: NavigationItemProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full flex items-center gap-2 p-2 rounded-md text-left transition-all duration-200 group",
        isActive && "bg-blue-500 text-white shadow-md", // Active state remains the same
        isCompleted && !isActive && "bg-green-500/10 text-green-400 hover:bg-green-500/20",
        hasErrors && !isActive && "bg-red-500/10 text-red-400 hover:bg-red-500/20",
        !isActive && !isCompleted && !hasErrors && "hover:bg-white/10 text-slate-300"
      )}
    >
      <div className="flex-1 min-w-0">
        <div className={cn(
          "font-medium text-xs truncate",
          isActive && "text-white"
        )}>
          {section.label}
        </div>
      </div>
      
      <div className="flex-shrink-0">
        {isCompleted && !hasErrors ? (
          <Check className={cn(
            "h-4 w-4",
            isActive ? "text-white" : "text-green-400"
          )} />
        ) : hasErrors ? (
          <AlertCircle className={cn(
            "h-4 w-4",
            isActive ? "text-white" : "text-red-400"
          )} />
        ) : (
          <ChevronRight className={cn(
            "h-3 w-3",
            isActive ? "text-white" : "text-slate-400"
          )} />
        )}
      </div>
    </button>
  )
}

function ProductSummary() {
  const { formData } = useProductForm();

  // Empty state: Displays before the user enters a product name.
  // This has also been styled to match the glassmorphism theme.
  if (!formData.name) {
    return (
      <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 shadow-lg p-4">
        <div className="text-center py-4">
          <p className="text-xs text-white/70 text-shadow">
            Product details will appear here as you fill out the form.
          </p>
        </div>
      </div>
    );
  }

  // Main component render with data.
  return (
    <div className="bg-white/10 backdrop-blur-xl rounded-lg border border-white/20 shadow-lg">
      {/* Header Section */}
      <div className="p-4">
        <p className="text-base font-semibold text-white text-shadow truncate">
          {formData.name}
        </p>
        {formData.base_sku && (
          <p className="text-xs text-white/80 text-shadow font-mono">
            {formData.base_sku}
          </p>
        )}
      </div>

      {/* Financials Section */}
      <div className="border-t border-white/10 p-4 space-y-3">
        {formData.price !== null && formData.price !== undefined && (
          <div className="flex justify-between items-center">
            <p className="text-xs text-white/70 text-shadow">Price</p>
            <p className="text-xs font-semibold text-white text-shadow">
              ${formData.price.toFixed(2)}
            </p>
          </div>
        )}
        {(formData.base_cost > 0 || formData.packaging_cost > 0) && (
          <div className="flex justify-between items-center">
            <p className="text-xs text-white/70 text-shadow">Total Cost</p>
            <p className="text-xs font-medium text-white/90 text-shadow">
              ${((formData.base_cost || 0) + (formData.packaging_cost || 0)).toFixed(2)}
            </p>
          </div>
        )}
      </div>

      {/* Attributes Section */}
      {(formData.has_variants && formData.variant_attributes.length > 0) || 
       (!formData.has_variants && (formData.size || formData.color)) ? (
        <div className="border-t border-white/10 p-4 space-y-3">
          <p className="text-xs font-medium text-white/80 text-shadow">Attributes</p>
          {!formData.has_variants ? (
            // Simple product attributes
            <>
              {formData.size && (
                <div className="flex justify-between items-center">
                  <p className="text-xs text-white/70 text-shadow">Size</p>
                  <p className="text-xs font-medium text-white/90 text-shadow">{formData.size}</p>
                </div>
              )}
              {formData.color && (
                <div className="flex justify-between items-center">
                  <p className="text-xs text-white/70 text-shadow">Color</p>
                  <p className="text-xs font-medium text-white/90 text-shadow">{formData.color}</p>
                </div>
              )}
            </>
          ) : (
            // Variable product attributes
            formData.variant_attributes.map((attr, index) => (
              <div key={index} className="flex justify-between items-center">
                <p className="text-xs text-white/70 text-shadow capitalize">{attr.name}</p>
                <p className="text-xs font-medium text-white/90 text-shadow">
                  {attr.values.join(', ')}
                </p>
              </div>
            ))
          )}
        </div>
      ) : null}

      {/* Inventory Section */}
      {(formData.stock_quantity !== undefined || formData.has_variants) && (
        <div className="border-t border-white/10 p-4 space-y-3">
          <div className="flex justify-between items-center">
            <p className="text-xs text-white/70 text-shadow">Type</p>
            <p className="text-xs font-medium text-white/90 text-shadow">
                {formData.has_variants ? 'Variable' : 'Simple'}
            </p>
          </div>
          {formData.stock_quantity !== undefined && !formData.has_variants && (
            <div className="flex justify-between items-center">
              <p className="text-xs text-white/70 text-shadow">Stock</p>
              <p className="text-xs font-semibold text-white text-shadow">
                {formData.stock_quantity} units
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
