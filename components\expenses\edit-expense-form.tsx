'use client'

import { useState, useEffect, useMemo } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useToast } from '@/components/ui/use-toast'
import { DatePicker } from '@/components/ui/date-picker'
import { CustomDropdown, CustomDropdownOption } from '@/components/ui/custom-dropdown'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from '@/components/ui/dialog'
import { getSupabaseClient, type ExpenseRow, type ExpenseUpdate } from '@/lib/supabase'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/lib/currency'

// Default categories
const DEFAULT_CATEGORIES: CustomDropdownOption[] = [
  { value: 'Office', label: 'Office Supplies' },
  { value: 'Travel', label: 'Travel & Transportation' },
  { value: 'Meals', label: 'Meals & Entertainment' },
  { value: 'Software', label: 'Software & Subscriptions' },
  { value: 'Marketing', label: 'Marketing & Advertising' },
  { value: 'Utilities', label: 'Utilities' },
  { value: 'Equipment', label: 'Equipment & Hardware' },
  { value: 'Professional', label: 'Professional Services' },
  { value: 'Insurance', label: 'Insurance' },
  { value: 'General', label: 'General' },
]

// Default payment methods
const DEFAULT_PAYMENT_METHODS: CustomDropdownOption[] = [
  { value: 'Cash', label: 'Cash' },
  { value: 'Credit Card', label: 'Credit Card' },
  { value: 'Debit Card', label: 'Debit Card' },
  { value: 'Bank Transfer', label: 'Bank Transfer' },
]

interface EditExpenseFormProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  expense: ExpenseRow
  onExpenseUpdated?: () => void
}

export function EditExpenseForm({ 
  open, 
  onOpenChange, 
  expense,
  onExpenseUpdated
}: EditExpenseFormProps) {
  // Form state
  const [expenseDate, setExpenseDate] = useState<Date>(new Date())
  const [amount, setAmount] = useState('')
  const [description, setDescription] = useState('')
  const [vendor, setVendor] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  
  // Options state for dropdowns
  const [categories, setCategories] = useState<CustomDropdownOption[]>(DEFAULT_CATEGORIES)
  const [paymentMethods, setPaymentMethods] = useState<CustomDropdownOption[]>(DEFAULT_PAYMENT_METHODS)
  
  // Memoize dropdown options to prevent excessive re-rendering
  const memoizedCategories = useMemo(() => categories, [categories])
  const memoizedPaymentMethods = useMemo(() => paymentMethods, [paymentMethods])
  
  const { toast } = useToast()
  const { user, organizationId } = useAuth()
  const { getCurrencySymbol } = useCurrency()
  const supabase = getSupabaseClient()

  // Load categories and payment methods
  useEffect(() => {
    if (user && organizationId) {
      loadCategories()
      loadPaymentMethods()
    }
  }, [user, organizationId])

  const loadCategories = async () => {
    if (!user?.id || !organizationId) return
    
    try {
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .eq('organization_id', organizationId)
        .order('name')

      if (error) {
        console.error('Error loading categories:', error)
        return
      }

      if (data && data.length > 0) {
        const categoryOptions = data.map(cat => ({
          value: cat.name,
          label: cat.name
        }))
        setCategories([...DEFAULT_CATEGORIES, ...categoryOptions])
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const loadPaymentMethods = async () => {
    if (!user?.id || !organizationId) return
    
    try {
      const { data, error } = await supabase
        .from('expenses')
        .select('payment_method')
        .eq('organization_id', organizationId)
        .not('payment_method', 'is', null)
        .order('payment_method')

      if (error) {
        console.error('Error loading payment methods:', error)
        return
      }

      if (data && data.length > 0) {
        const uniquePaymentMethods = Array.from(new Set(data.map(expense => expense.payment_method)))
        const paymentMethodOptions = uniquePaymentMethods.map((method, index) => ({
          value: method,
          label: method,
          key: `db-${method}-${index}` // Add unique key to prevent duplicates
        }))
        
        // Merge with default options, avoiding duplicates
        const mergedOptions = [...DEFAULT_PAYMENT_METHODS]
        paymentMethodOptions.forEach(dbOption => {
          if (!mergedOptions.some(defaultOption => defaultOption.value === dbOption.value)) {
            mergedOptions.push(dbOption)
          }
        })
        
        setPaymentMethods(mergedOptions)
      }
    } catch (error) {
      console.error('Error loading payment methods:', error)
    }
  }

  // Initialize form with expense data
  useEffect(() => {
    if (open && expense) {
      setExpenseDate(new Date(expense.expense_date || expense.created_at))
      setAmount(expense.amount.toString())
      setDescription(expense.description || '')
      setVendor(expense.vendor || '')
      setSelectedCategory(expense.category)
      setSelectedPaymentMethod(expense.payment_method || '')
    }
  }, [open, expense])

  const handleAddCategory = (newCategory: CustomDropdownOption) => {
    if (!categories.find(cat => cat.value === newCategory.value)) {
      setCategories(prev => [...prev, newCategory])
    }
  }

  const handleAddPaymentMethod = (newPaymentMethod: CustomDropdownOption) => {
    if (!paymentMethods.find(method => method.value === newPaymentMethod.value)) {
      setPaymentMethods(prev => [...prev, newPaymentMethod])
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "Please sign in to update expenses.",
        variant: "destructive",
      })
      return
    }

    if (!amount || !description || !selectedCategory || !selectedPaymentMethod) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields.",
        variant: "destructive",
      })
      return
    }

    setIsLoading(true)

    try {
      const expenseUpdate: ExpenseUpdate = {
        amount: parseFloat(amount),
        description: description.trim(),
        vendor: vendor.trim() || null,
        category: selectedCategory,
        payment_method: selectedPaymentMethod,
        expense_date: expenseDate.toISOString().split('T')[0], // Format as YYYY-MM-DD
      }

      const { error } = await supabase
        .from('expenses')
        .update(expenseUpdate)
        .eq('id', expense.id)
        .eq('organization_id', organizationId)

      if (error) {
        console.error('Supabase error:', error)
        toast({
          title: "Error Updating Expense",
          description: `Failed to update expense: ${error.message}`,
          variant: "destructive",
        })
      } else {
        toast({
          title: "Expense Updated!",
          description: `Successfully updated $${amount} expense: ${description}`,
        })
        
        // Reload categories and payment methods to include any new ones
        loadCategories()
        loadPaymentMethods()
        
        // Close the dialog and notify parent
        onOpenChange(false)
        onExpenseUpdated?.()
      }
    } catch (error) {
      console.error('Error updating expense:', error)
      toast({
        title: "Unexpected Error",
        description: "Failed to update expense. Please try again.",
        variant: "destructive",
      })
    } finally {
      // Always reset loading state
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-2xl p-0">
        <DialogHeader className="border-b p-6">
          <DialogTitle className="text-lg font-semibold">Edit Expense</DialogTitle>
          <DialogDescription className="text-slate-500">
            Update the details of your expense record.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-8 p-6">
            {/* Left Column - Primary Details */}
            <div className="space-y-6">
              {/* Amount - Hero Field */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Amount ({getCurrencySymbol()}) *</Label>
                <Input
                  id="edit-amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  placeholder="0.00"
                  required
                  className="h-12 text-xl placeholder:text-xs"
                />
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="edit-description" className="text-sm font-medium">Description *</Label>
                <Textarea
                  id="edit-description"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Enter description"
                  required
                  rows={3}
                  className="text-xs placeholder:text-xs"
                />
              </div>

              {/* Vendor */}
              <div className="space-y-2">
                <Label htmlFor="edit-vendor" className="text-sm font-medium">Vendor/Supplier</Label>
                <Input
                  id="edit-vendor"
                  value={vendor}
                  onChange={(e) => setVendor(e.target.value)}
                  placeholder="Enter vendor name"
                  className="h-8 text-xs placeholder:text-xs"
                />
              </div>
            </div>

            {/* Right Column - Organization & Metadata */}
            <div className="bg-slate-50 p-6 rounded-lg space-y-6">
              {/* Expense Date */}
              <div className="space-y-2">
                <Label className="text-sm font-medium">Date *</Label>
                <DatePicker
                  date={expenseDate}
                  onDateChange={(date) => date && setExpenseDate(date)}
                  className="h-8 text-xs"
                />
              </div>

              {/* Category Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="edit-category" className="text-sm font-medium">Category *</Label>
                <CustomDropdown
                  options={memoizedCategories}
                  value={selectedCategory}
                  onValueChange={(newValue) => {
                    setSelectedCategory(newValue)
                  }}
                  onAddCustomOption={(newOption) => handleAddCategory(newOption as CustomDropdownOption)}
                  placeholder="Select or add category"
                  customPlaceholder="Add custom category"
                  allowCustom={true}
                  className="h-8 text-xs placeholder:text-xs"
                />
              </div>

              {/* Payment Method Dropdown */}
              <div className="space-y-2">
                <Label htmlFor="edit-payment-method" className="text-sm font-medium">Payment Method *</Label>
                <CustomDropdown
                  options={memoizedPaymentMethods}
                  value={selectedPaymentMethod}
                  onValueChange={(newValue) => {
                    setSelectedPaymentMethod(newValue)
                  }}
                  onAddCustomOption={handleAddPaymentMethod}
                  placeholder="Select payment method"
                  customPlaceholder="Add custom payment method"
                  allowCustom={true}
                  className="h-8 text-xs placeholder:text-xs"
                />
              </div>

              {/* Expense ID */}
              <div className="pt-4">
                <dl className="space-y-1">
                  <dt className="text-xs font-medium text-slate-500">Expense ID</dt>
                  <dd className="text-sm font-medium">
                    {expense.expense_id || 'Not assigned'}
                  </dd>
                </dl>
              </div>
            </div>
          </div>
          
          <DialogFooter className="border-t bg-slate-50 p-6">
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => onOpenChange(false)}
              className="h-8 text-xs"
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              size="sm"
              className="h-8 text-xs"
              disabled={isLoading}
            >
              {isLoading ? 'Updating Expense...' : 'Update Expense'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}