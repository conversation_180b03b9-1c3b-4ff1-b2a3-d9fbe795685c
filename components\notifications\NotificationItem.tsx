'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { format, parseISO } from 'date-fns'
import { cn } from '@/lib/utils'
import {
  ChevronDown,
  ChevronUp,
  CheckCircle,
  AlertTriangle,
  Info,
  X,
  Archive,
  ShoppingCart,
  Package,
  FileText,
  DollarSign,
  Plus,
  Minus,
  MoreHorizontal,
  Eye,
  EyeOff,
  Trash2,
} from 'lucide-react'
import { ExpandedNotificationDetails } from './ExpandedNotificationDetails'
import { NotificationActionDialog } from './NotificationActionDialog'
import { useNotification } from './NotificationContext'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications?: any[]
}

interface NotificationItemProps {
  notification: Notification
  isExpanded: boolean
  toggleExpansion: (id: string) => void
  formatDate: (dateString: string) => string
  getPanelNotificationIconProps: (type: string) => {
    Icon: any
    bgClass: string
    iconClass: string
  }
  getPriorityBadge: (priority?: string) => JSX.Element | null
  formatFullDate: (dateString: string) => string
}

export function NotificationItem({
  notification,
  isExpanded,
  toggleExpansion,
  formatDate,
  getPanelNotificationIconProps,
  getPriorityBadge,
  formatFullDate
}: NotificationItemProps) {
  // Add defensive check for notification
  if (!notification) {
    return null
  }
  
  const { Icon, bgClass, iconClass } = getPanelNotificationIconProps(notification.type || 'info')
  const isGrouped = notification.related_notifications && notification.related_notifications.length > 0
  const [confirmAction, setConfirmAction] = useState<'delete' | 'archive' | 'mark-unread' | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)
  const { markAsRead, markAsUnread, archiveNotification, deleteNotification } = useNotification()

  const handleMarkAsRead = async () => {
    if (!notification.is_read) {
      await markAsRead(notification.id)
    }
  }

  const handleMarkAsUnread = async () => {
    if (notification.is_read) {
      setConfirmAction('mark-unread')
    }
  }

  const handleArchive = () => {
    setConfirmAction('archive')
  }

  const handleDelete = () => {
    setConfirmAction('delete')
  }

  const executeAction = async () => {
    if (!confirmAction || !notification.id) return

    setIsProcessing(true)
    try {
      switch (confirmAction) {
        case 'mark-unread':
          await markAsUnread(notification.id)
          break
        case 'archive':
          await archiveNotification(notification.id)
          break
        case 'delete':
          await deleteNotification(notification.id)
          break
      }
    } finally {
      setIsProcessing(false)
      setConfirmAction(null)
    }
  }

  // Add defensive checks for required notification properties
  const notificationTitle = notification.title || 'Untitled Notification'
  const notificationMessage = notification.message || 'No message content'
  const notificationCreatedAt = notification.created_at || ''

  return (
    <>
      <Card 
        className={cn(
          "group relative transition-all duration-200",
          "hover:bg-gray-50 border-l-2 hover:shadow-xs",
          !notification.is_read
            ? "bg-blue-50/30 border-l-blue-500"
            : "border-l-transparent hover:border-l-gray-200"
        )}
      >
        <CardContent className="p-3">
          <div className="flex items-start gap-2.5">
            <div className={cn(
              "flex-shrink-0 w-6 h-6 rounded-lg flex items-center justify-center mt-0.5",
              bgClass
            )}>
              <Icon className={cn("h-3 w-3", iconClass)} />
            </div>

            <div className="flex-1 min-w-0">
              {/* Notification header with title and grouping badge */}
              <div className="flex items-start justify-between gap-2">
                <div className="flex items-center gap-1.5 min-w-0">
                  <p className={cn(
                    "text-sm font-medium truncate",
                    !notification.is_read ? "text-gray-900" : "text-gray-700"
                  )}>
                    {notificationTitle}
                  </p>
                  {isGrouped && (
                    <Badge variant="secondary" className="text-[10px] px-1.5 py-0.5 flex-shrink-0">
                      {notification.related_notifications!.length} related
                    </Badge>
                  )}
                </div>
                
                {/* Timestamp and action menu */}
                <div className="flex items-center gap-1 flex-shrink-0">
                  <span className="text-[11px] text-gray-500 whitespace-nowrap">
                    {formatDate(notificationCreatedAt)}
                  </span>
                  {!notification.is_read && (
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                  )}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 text-gray-500 hover:text-gray-700"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <span className="sr-only">Open notification menu</span>
                        <MoreHorizontal className="h-3.5 w-3.5" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="w-48">
                      {!notification.is_read ? (
                        <DropdownMenuItem onClick={handleMarkAsRead} className="text-xs">
                          <Eye className="mr-2 h-3.5 w-3.5" />
                          Mark as read
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem onClick={handleMarkAsUnread} className="text-xs">
                          <EyeOff className="mr-2 h-3.5 w-3.5" />
                          Mark as unread
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem onClick={handleArchive} className="text-xs">
                        <Archive className="mr-2 h-3.5 w-3.5" />
                        Archive
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={handleDelete} className="text-xs text-red-600 focus:text-red-600">
                        <Trash2 className="mr-2 h-3.5 w-3.5" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              
              {/* Notification content */}
              <div className="flex items-start justify-between gap-2 mt-1">
                <p className={cn(
                  "text-xs text-gray-600 flex-1 min-w-0",
                  isExpanded ? "" : "line-clamp-2"
                )}>
                  {notificationMessage}
                </p>
                
                {/* Expand/collapse chevron for long messages, grouped notifications, or expense notifications */}
                {(isGrouped || notificationMessage.length > 100 || notification.type === 'expense_added') && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      toggleExpansion(notification.id)
                    }}
                    className="h-4 w-4 p-0 text-gray-500 hover:text-gray-700"
                  >
                    {isExpanded ? (
                      <ChevronUp className="h-3 w-3" />
                    ) : (
                      <ChevronDown className="h-3 w-3" />
                    )}
                  </Button>
                )}
              </div>

              {/* Expandable Content */}
              {isExpanded && (
                <ExpandedNotificationDetails
                  notification={notification}
                  isGrouped={!!isGrouped}
                  getPanelNotificationIconProps={getPanelNotificationIconProps}
                  getPriorityBadge={getPriorityBadge}
                  formatDate={formatDate}
                  formatFullDate={formatFullDate}
                />
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <NotificationActionDialog
        open={!!confirmAction}
        onOpenChange={(open) => !open && setConfirmAction(null)}
        action={confirmAction as any}
        notificationTitle={notificationTitle}
        onConfirm={executeAction}
        isProcessing={isProcessing}
      />
    </>
  )
}