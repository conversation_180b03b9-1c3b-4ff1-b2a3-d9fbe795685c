'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Bell } from 'lucide-react'

interface EmptyStateProps {
  searchQuery: string
  readFilter: string
  typeFilter: string
  clearFilters: () => void
}

export function EmptyState({
  searchQuery,
  readFilter,
  typeFilter,
  clearFilters
}: EmptyStateProps) {
  const hasActiveFilters = searchQuery || readFilter !== 'all' || typeFilter !== 'all'

  return (
    <Card className="border-dashed border-2 border-gray-200">
      <CardContent className="p-8 text-center">
        <div className="w-12 h-12 mx-auto mb-3 bg-gray-100 rounded-full flex items-center justify-center">
          <Bell className="w-5 h-5 text-gray-400" />
        </div>
        <h3 className="text-sm font-medium text-gray-900 mb-1">No notifications</h3>
        <p className="text-xs text-gray-500 mb-3">
          {hasActiveFilters
            ? 'No notifications match your current filters.'
            : 'You\'re all caught up! Check back later for new notifications.'}
        </p>
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={clearFilters}
            className="mt-1 h-7 px-3 text-xs"
          >
            Clear filters
          </Button>
        )}
      </CardContent>
    </Card>
  )
}