'use client'

import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'
import { Plus, X } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

interface SimpleAttribute {
  name: string
  value: string
}

// Common attribute options
const COMMON_ATTRIBUTES = [
  { value: 'size', label: 'Size' },
  { value: 'color', label: 'Color' },
  { value: 'material', label: 'Material' },
  { value: 'weight', label: 'Weight' },
  { value: 'style', label: 'Style' },
  { value: 'finish', label: 'Finish' },
  { value: 'brand', label: 'Brand' },
  { value: 'model', label: 'Model' }
]

// Common values for each attribute type
const ATTRIBUTE_VALUES: Record<string, { value: string; label: string }[]> = {
  size: [
    { value: 'xs', label: 'Extra Small (XS)' },
    { value: 's', label: 'Small (S)' },
    { value: 'm', label: 'Medium (M)' },
    { value: 'l', label: 'Large (L)' },
    { value: 'xl', label: 'Extra Large (XL)' },
    { value: 'xxl', label: 'XXL' }
  ],
  color: [
    { value: 'red', label: 'Red' },
    { value: 'blue', label: 'Blue' },
    { value: 'green', label: 'Green' },
    { value: 'black', label: 'Black' },
    { value: 'white', label: 'White' },
    { value: 'gray', label: 'Gray' },
    { value: 'silver', label: 'Silver' },
    { value: 'gold', label: 'Gold' }
  ],
  material: [
    { value: 'cotton', label: 'Cotton' },
    { value: 'polyester', label: 'Polyester' },
    { value: 'leather', label: 'Leather' },
    { value: 'metal', label: 'Metal' },
    { value: 'plastic', label: 'Plastic' },
    { value: 'wood', label: 'Wood' },
    { value: 'glass', label: 'Glass' }
  ],
  style: [
    { value: 'casual', label: 'Casual' },
    { value: 'formal', label: 'Formal' },
    { value: 'sporty', label: 'Sporty' },
    { value: 'vintage', label: 'Vintage' },
    { value: 'modern', label: 'Modern' },
    { value: 'classic', label: 'Classic' }
  ]
}

export function SimpleProductAttributes() {
  const { formData, updateFormData, errors } = useProductForm()
  const [attributes, setAttributes] = useState<SimpleAttribute[]>([])
  const [newAttributeName, setNewAttributeName] = useState('')
  const [newAttributeValue, setNewAttributeValue] = useState('')

  // Initialize attributes from existing size/color data
  React.useEffect(() => {
    const initialAttributes: SimpleAttribute[] = []
    if (formData.size) {
      initialAttributes.push({ name: 'size', value: formData.size })
    }
    if (formData.color) {
      initialAttributes.push({ name: 'color', value: formData.color })
    }
    setAttributes(initialAttributes)
  }, [])

  const addAttribute = () => {
    if (!newAttributeName || !newAttributeValue) return

    const newAttribute: SimpleAttribute = {
      name: newAttributeName,
      value: newAttributeValue
    }

    const updatedAttributes = [...attributes, newAttribute]
    setAttributes(updatedAttributes)

    // Update form data for backward compatibility
    if (newAttributeName === 'size') {
      updateFormData('size', newAttributeValue)
    } else if (newAttributeName === 'color') {
      updateFormData('color', newAttributeValue)
    }

    // Reset form
    setNewAttributeName('')
    setNewAttributeValue('')
  }

  const removeAttribute = (index: number) => {
    const attributeToRemove = attributes[index]
    const updatedAttributes = attributes.filter((_, i) => i !== index)
    setAttributes(updatedAttributes)

    // Clear form data for backward compatibility
    if (attributeToRemove.name === 'size') {
      updateFormData('size', '')
    } else if (attributeToRemove.name === 'color') {
      updateFormData('color', '')
    }
  }

  const getValueOptions = (attributeName: string) => {
    return ATTRIBUTE_VALUES[attributeName] || []
  }

  return (
    <div className="space-y-4">
      <div className="border-t border-gray-200 pt-6">
        <h4 className={`${designSystem.typography.label} text-gray-700 mb-2`}>
          Product Attributes
        </h4>
        <p className={`${designSystem.typography.caption} text-gray-600 mb-4`}>
          Add specific attributes for this simple product (optional)
        </p>

        {/* Existing Attributes */}
        {attributes.length > 0 && (
          <div className="space-y-2 mb-4">
            {attributes.map((attribute, index) => (
              <div key={index} className="flex items-center gap-2 p-2 bg-gray-50 rounded-md">
                <span className="text-sm font-medium text-gray-700 capitalize">
                  {attribute.name}:
                </span>
                <span className="text-sm text-gray-600 capitalize">
                  {attribute.value}
                </span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => removeAttribute(index)}
                  className="ml-auto h-6 w-6 p-0 text-gray-400 hover:text-red-500"
                >
                  <X className="w-3 h-3" />
                </Button>
              </div>
            ))}
          </div>
        )}

        {/* Add New Attribute */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-3 items-end">
          <FormField label="Attribute Type">
            <SimpleDropdown
              value={newAttributeName}
              onValueChange={setNewAttributeName}
              placeholder="Select attribute type"
              options={COMMON_ATTRIBUTES}
              allowCustom={true}
              customPlaceholder="Enter custom attribute"
              className="h-8 text-xs"
              optionClassName="text-xs"
              size="xs"
              textsize="xs"
            />
          </FormField>

          <FormField label="Value">
            {newAttributeName && getValueOptions(newAttributeName).length > 0 ? (
              <SimpleDropdown
                value={newAttributeValue}
                onValueChange={setNewAttributeValue}
                placeholder="Select value"
                options={getValueOptions(newAttributeName)}
                allowCustom={true}
                customPlaceholder="Enter custom value"
                className="h-8 text-xs"
                optionClassName="text-xs"
                size="xs"
                textsize="xs"
              />
            ) : (
              <Input
                value={newAttributeValue}
                onChange={(e) => setNewAttributeValue(e.target.value)}
                placeholder="Enter attribute value"
                className={getInputClasses(false)}
              />
            )}
          </FormField>

          <Button
            type="button"
            onClick={addAttribute}
            disabled={!newAttributeName || !newAttributeValue}
            className="h-8 px-3"
          >
            <Plus className="w-4 h-4 mr-1" />
            Add
          </Button>
        </div>
      </div>
    </div>
  )
}