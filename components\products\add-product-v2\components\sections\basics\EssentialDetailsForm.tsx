'use client'

import React, { useEffect, useState } from 'react'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Button } from '@/components/ui/button'
import { RefreshCw, AlertCircle } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'
import { SimpleProductAttributes } from './SimpleProductAttributes'

export function EssentialDetailsForm() {
  const { formData, updateFormData, errors, categories } = useProductForm()
  const [isGeneratingSku, setIsGeneratingSku] = useState(false)

  // Auto-generate SKU when product name changes
  const generateSku = (productName: string) => {
    if (!productName.trim()) return ''

    // Get first 4 letters of product name (remove spaces and special chars)
    const namePrefix = productName
      .trim()
      .replace(/[^a-zA-Z]/g, '')
      .substring(0, 4)
      .toUpperCase()

    // Get current date in YYYYMMDD format
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1).padStart(2, '0')
    const day = String(today.getDate()).padStart(2, '0')
    const dateString = `${year}${month}${day}`

    // Get category ID (last 2 digits, or random if no category selected)
    let categoryId = '00'
    if (formData.category_id && categories.length > 0) {
      const selectedCategory = categories.find(cat => cat.id === formData.category_id)
      if (selectedCategory) {
        // Use last 2 characters of category ID
        categoryId = selectedCategory.id.slice(-2).toUpperCase()
      }
    } else {
      // Generate random 2-digit number if no category
      categoryId = String(Math.floor(Math.random() * 100)).padStart(2, '0')
    }

    // Format: TEBR-20250901-76
    const sku = `${namePrefix}-${dateString}-${categoryId}`
    return sku
  }

  const handleNameChange = (value: string) => {
    updateFormData('name', value)

    // Auto-generate SKU if it's empty
    if (!formData.base_sku) {
      const newSku = generateSku(value)
      updateFormData('base_sku', newSku)
    }
  }

  const handleGenerateSkuClick = () => {
    if (!formData.name.trim()) return

    setIsGeneratingSku(true)
    setTimeout(() => {
      const newSku = generateSku(formData.name)
      updateFormData('base_sku', newSku)
      setIsGeneratingSku(false)
    }, 300) // Small delay for UX feedback
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Essential Details
        </h3>
        <p className={designSystem.typography.caption}>
          Provide the core information that identifies your product
        </p>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {/* Product Name */}
        <FormField
          label="Product Name"
          required
          error={errors.name}
        >
          <Input
            value={formData.name}
            onChange={(e) => handleNameChange(e.target.value)}
            placeholder="Enter product name"
            className={getInputClasses(!!errors.name)}
          />
        </FormField>

        {/* Description */}
        <FormField
          label="Description"
          error={errors.description}
        >
          <Textarea
            value={formData.description}
            onChange={(e) => updateFormData('description', e.target.value)}
            placeholder="Describe your product (optional)"
            rows={3}
            className={`${getInputClasses(!!errors.description)} resize-none`}
          />
        </FormField>

        {/* Brand and SKU Row */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Brand */}
          <FormField
            label="Brand"
            error={errors.brand}
          >
            <Input
              value={formData.brand}
              onChange={(e) => updateFormData('brand', e.target.value)}
              placeholder="Brand name"
              className={getInputClasses(!!errors.brand)}
            />
          </FormField>

          {/* Base SKU */}
          <FormField
            label="Base SKU"
            required
            error={errors.base_sku}
            action={
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={handleGenerateSkuClick}
                disabled={!formData.name.trim() || isGeneratingSku}
                className="h-6 w-6"
              >
                {isGeneratingSku ? (
                  <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin" />
                ) : (
                  <RefreshCw className="w-3 h-3" />
                )}
              </Button>
            }
          >
            <Input
              value={formData.base_sku}
              onChange={(e) => updateFormData('base_sku', e.target.value.toUpperCase())}
              placeholder="PROD-SKU"
              className={getInputClasses(!!errors.base_sku)}
            />
          </FormField>
        </div>
      </div>

      {/* SKU Help Text */}
      {formData.name && !formData.base_sku && (
        <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
          <AlertCircle className="w-4 h-4 text-amber-600 mt-0.5 flex-shrink-0" />
          <div className={designSystem.typography.caption}>
            <p className="text-amber-700 font-medium">SKU Required</p>
            <p className="text-amber-600">
              Click "Generate" to create a SKU automatically, or enter one manually.
            </p>
          </div>
        </div>
      )}

      {/* SKU Preview for Variable Products */}
      {formData.has_variants && formData.base_sku && (
        <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
          <p className={`${designSystem.typography.caption} text-blue-700 font-medium mb-1`}>
            SKU Preview for Variants
          </p>
          <p className={`${designSystem.typography.caption} text-blue-600`}>
            Variants will use this base: <code className="bg-blue-100 px-1 rounded">{formData.base_sku}-[VARIANT]</code>
          </p>
          <p className={`${designSystem.typography.caption} text-blue-600`}>
            Example: <code className="bg-blue-100 px-1 rounded">{formData.base_sku}-SM-RED</code>
          </p>
        </div>
      )}

      {/* Simple Product Attributes (only for simple products) */}
      {!formData.has_variants && (
        <SimpleProductAttributes />
      )}
    </div>
  )
}