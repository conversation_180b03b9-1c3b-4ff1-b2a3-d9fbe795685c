'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  CheckCircle,
  AlertTriangle,
  Info,
  Clock,
  Archive,
  Trash2,
  EyeOff,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  X
} from 'lucide-react'
import { format, formatDistanceToNow, parseISO, isToday, isYesterday } from 'date-fns'
import { cn } from '@/lib/utils'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
}

interface NotificationCardProps {
  notification: Notification
  isSelected: boolean
  isExpanded: boolean
  isHighlighted: boolean
  onToggleSelection: () => void
  onToggleExpansion: () => void
  onMarkAsRead: () => void
  onArchive: () => void
  onDelete: () => void
  onOpenDetail?: () => void
  viewMode?: 'list' | 'cards' | 'grouped'
}

export function NotificationCard({
  notification,
  isSelected,
  isExpanded,
  isHighlighted,
  onToggleSelection,
  onToggleExpansion,
  onMarkAsRead,
  onArchive,
  onDelete,
  onOpenDetail,
  viewMode = 'cards'
}: NotificationCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const [loadingAction, setLoadingAction] = useState<string | null>(null)
  const router = useRouter()

  // Enhanced action handlers with loading states
  const handleActionWithLoading = async (actionType: string, action: () => void) => {
    setLoadingAction(actionType)
    try {
      await action()
    } finally {
      setLoadingAction(null)
    }
  }

  // Get enhanced notification icon with background
  const getNotificationIconProps = (type: string) => {
    switch (type) {
      case 'success':
        return {
          Icon: CheckCircle,
          bgClass: 'border border-green-200',
          iconClass: 'text-green-600',
        }
      case 'warning':
        return {
          Icon: AlertTriangle,
          bgClass: 'border border-yellow-200',
          iconClass: 'text-yellow-600',
        }
      case 'product_deleted':
        return {
          Icon: X,
          bgClass: 'border border-red-200',
          iconClass: 'text-red-600',
        }
      case 'overdue_invoice':
        return {
          Icon: AlertTriangle,
          bgClass: 'border border-red-200',
          iconClass: 'text-red-600',
        }
      case 'new_order':
        return {
          Icon: Info,
          bgClass: 'border border-blue-200',
          iconClass: 'text-blue-600',
        }
      case 'info':
      default:
        return {
          Icon: Info,
          bgClass: 'border border-blue-200',
          iconClass: 'text-blue-600',
        }
    }
  }

  // Get priority badge styling
  const getPriorityBadge = (priority?: string) => {
    if (!priority) return null
    
    const variants = {
      low: { variant: 'secondary' as const, text: 'Low', color: 'text-gray-600' },
      medium: { variant: 'default' as const, text: 'Medium', color: 'text-blue-600' },
      high: { variant: 'destructive' as const, text: 'High', color: 'text-orange-600' },
      urgent: { variant: 'destructive' as const, text: 'Urgent', color: 'text-red-600' }
    }
    
    const config = variants[priority as keyof typeof variants]
    if (!config) return null
    
    return (
      <Badge variant={config.variant} className={cn("text-xs", config.color)}>
        {config.text}
      </Badge>
    )
  }

  // Format notification time
  const formatNotificationTime = (dateString: string) => {
    const date = parseISO(dateString)
    
    if (isToday(date)) {
      return format(date, 'h:mm a')
    } else if (isYesterday(date)) {
      return 'Yesterday'
    } else if (Date.now() - date.getTime() < 7 * 24 * 60 * 60 * 1000) {
      return format(date, 'EEEE')
    } else {
      return format(date, 'MMM d')
    }
  }

  const iconProps = getNotificationIconProps(notification.type)
  const { Icon, bgClass, iconClass, borderClass } = iconProps

  // Handle navigation to related entity
  const handleNavigateToEntity = () => {
    if (notification.related_entity_type && notification.related_entity_id) {
      const routes = {
        product: `/dashboard/products/${notification.related_entity_id}`,
        order: `/dashboard/orders/${notification.related_entity_id}`,
        invoice: `/dashboard/invoices/${notification.related_entity_id}`,
        customer: `/dashboard/customers/${notification.related_entity_id}`
      }
      
      const route = routes[notification.related_entity_type as keyof typeof routes]
      if (route) {
        router.push(route)
      }
    }
  }

  return (
    <Card
      id={`notification-${notification.id}`}
      className={cn(
        "notification-card-hover cursor-pointer group",
        "transition-all duration-300 ease-out",
        !notification.is_read && "bg-blue-50/50 border-blue-200",
        isHighlighted && "ring-2 ring-blue-500 ring-offset-2 animate-notification-bounce",
        isSelected && "ring-2 ring-blue-300 shadow-lg",
        viewMode === 'list' && "shadow-sm",
        "hover:shadow-lg hover:border-gray-300"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onOpenDetail}
    >
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          {/* Selection Checkbox */}
          <div className="flex items-center pt-1">
            <Checkbox
              checked={isSelected}
              onCheckedChange={onToggleSelection}
              className="data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"
            />
          </div>

          {/* Notification Icon */}
          <div className={cn(
            "flex-shrink-0 w-12 h-12 rounded-lg flex items-center justify-center border",
            bgClass
          )}>
            <Icon className={cn("w-6 h-6", iconClass)} />
          </div>

          {/* Notification Content */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-4">
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h3 className={cn(
                    "text-base font-semibold truncate",
                    !notification.is_read ? "text-gray-900" : "text-gray-700"
                  )}>
                    {notification.title}
                  </h3>
                  {!notification.is_read && (
                    <div className="w-2 h-2 bg-blue-500 rounded-full flex-shrink-0" />
                  )}
                </div>
                
                <div className="flex items-center gap-2 mb-2">
                  {getPriorityBadge(notification.priority)}
                  {notification.category && (
                    <Badge variant="outline" className="text-xs">
                      {notification.category}
                    </Badge>
                  )}
                  <div className="flex items-center gap-1 text-sm text-gray-500">
                    <Clock className="w-4 h-4" />
                    <span>{formatNotificationTime(notification.created_at)}</span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className={cn(
                "flex items-center gap-1 transition-all duration-300 ease-out",
                isHovered ? "opacity-100 translate-x-0" : "opacity-0 translate-x-2"
              )}>
                {!notification.is_read && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleActionWithLoading('read', onMarkAsRead)}
                    disabled={loadingAction === 'read'}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-blue-600 disabled:opacity-50"
                    title="Mark as read"
                  >
                    {loadingAction === 'read' ? (
                      <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />
                    ) : (
                      <EyeOff className="h-4 w-4" />
                    )}
                  </Button>
                )}

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleActionWithLoading('archive', onArchive)}
                  disabled={loadingAction === 'archive'}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-gray-600 disabled:opacity-50"
                  title="Archive"
                >
                  {loadingAction === 'archive' ? (
                    <div className="w-4 h-4 border-2 border-gray-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Archive className="h-4 w-4" />
                  )}
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleActionWithLoading('delete', onDelete)}
                  disabled={loadingAction === 'delete'}
                  className="h-8 w-8 p-0 text-gray-400 hover:text-red-600 disabled:opacity-50"
                  title="Delete"
                >
                  {loadingAction === 'delete' ? (
                    <div className="w-4 h-4 border-2 border-red-600 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4" />
                  )}
                </Button>
                
                {onOpenDetail && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onOpenDetail}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-blue-600"
                    title="View full details"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                )}

                {notification.related_entity_type && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleNavigateToEntity}
                    className="h-8 w-8 p-0 text-gray-400 hover:text-green-600"
                    title="Go to related item"
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                )}
              </div>
            </div>

            {/* Notification Message */}
            <p className={cn(
              "text-sm leading-relaxed",
              !notification.is_read ? "text-gray-700" : "text-gray-600",
              isExpanded ? "" : "line-clamp-2"
            )}>
              {notification.message}
            </p>

            {/* Expandable Content */}
            {notification.message.length > 100 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onToggleExpansion}
                className="mt-2 h-auto p-0 text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                {isExpanded ? (
                  <>
                    <ChevronUp className="w-4 h-4 mr-1" />
                    Show less
                  </>
                ) : (
                  <>
                    <ChevronDown className="w-4 h-4 mr-1" />
                    Show more
                  </>
                )}
              </Button>
            )}

            {/* Expanded Details */}
            {isExpanded && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Type:</span>
                    <span className="ml-2 text-gray-600">{notification.type}</span>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Created:</span>
                    <span className="ml-2 text-gray-600">
                      {format(parseISO(notification.created_at), 'MMM d, yyyy h:mm a')}
                    </span>
                  </div>
                  {notification.category && (
                    <div>
                      <span className="font-medium text-gray-700">Category:</span>
                      <span className="ml-2 text-gray-600">{notification.category}</span>
                    </div>
                  )}
                  {notification.related_entity_type && (
                    <div>
                      <span className="font-medium text-gray-700">Related:</span>
                      <span className="ml-2 text-gray-600 capitalize">
                        {notification.related_entity_type}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
