'use client'

import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  ChevronDown,
  ChevronUp,
  CheckCircle,
  AlertTriangle,
  Info,
  X,
  Archive,
  ShoppingCart,
  Package,
  FileText,
  DollarSign,
  Plus,
  Minus
} from 'lucide-react'
import { ExpandedNotificationDetails } from './ExpandedNotificationDetails'

interface Notification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications?: Notification[]
}

interface GroupedNotification {
  id: string
  title: string
  message: string
  type: string
  created_at: string
  is_read: boolean
  is_archived: boolean
  priority?: 'low' | 'medium' | 'high' | 'urgent'
  category?: string
  related_entity_type?: string
  related_entity_id?: string
  related_notifications: Notification[]
}

interface NotificationGroupProps {
  group: {
    title: string
    notifications: (Notification | GroupedNotification)[]
    count: number
  }
  expandedNotifications: string[]
  toggleNotificationExpansion: (id: string) => void
  formatDate: (dateString: string) => string
  getPanelNotificationIconProps: (type: string) => {
    Icon: any
    bgClass: string
    iconClass: string
  }
  getPriorityBadge: (priority?: string) => JSX.Element | null
  formatFullDate: (dateString: string) => string
}

export function NotificationGroup({
  group,
  expandedNotifications,
  toggleNotificationExpansion,
  formatDate,
  getPanelNotificationIconProps,
  getPriorityBadge,
  formatFullDate
}: NotificationGroupProps) {
  return (
    <div key={group.title}>
      <div className="flex items-center gap-2 mb-3">
        <h3 className="text-sm font-semibold text-gray-900">{group.title}</h3>
        <Badge variant="secondary" className="text-xs px-1.5 py-0.5">
          {group.count}
        </Badge>
      </div>
      <div className="space-y-2">
        {group.notifications.map((notification) => {
          // Type guard to check if this is a GroupedNotification
          const isGroupedNotification = (n: Notification | GroupedNotification): n is GroupedNotification => {
            return 'related_notifications' in n;
          };

          const { Icon, bgClass, iconClass } = getPanelNotificationIconProps(notification.type)
          const isExpanded = expandedNotifications.includes(notification.id)
          
          // Check if this is a grouped notification
          const isGrouped = isGroupedNotification(notification) && notification.related_notifications.length > 0
          
          return (
            <Card 
              key={notification.id}
              className={cn(
                "group relative transition-all duration-200",
                "hover:bg-gray-50 border-l-2 hover:shadow-xs",
                !notification.is_read
                  ? "bg-blue-50/30 border-l-blue-500"
                  : "border-l-transparent hover:border-l-gray-200"
              )}
            >
              <CardContent className="p-3">
                <div className="flex items-start gap-2.5">
                  <div className={cn(
                    "flex-shrink-0 w-6 h-6 rounded-lg flex items-center justify-center mt-0.5",
                    bgClass
                  )}>
                    <Icon className={cn("h-3 w-3", iconClass)} />
                  </div>

                  <div className="flex-1 min-w-0">
                    {/* Notification header with title */}
                    <div className="flex items-start justify-between gap-2">
                      <p className={cn(
                        "text-sm font-medium truncate",
                        !notification.is_read ? "text-gray-900" : "text-gray-700"
                      )}>
                        {notification.title}
                      </p>
                      
                      {/* Timestamp */}
                      <div className="flex items-center gap-1 flex-shrink-0">
                        <span className="text-[11px] text-gray-500 whitespace-nowrap">
                          {notification.created_at ? formatDate(notification.created_at) : 'Unknown date'}
                        </span>
                        {!notification.is_read && (
                          <div className="w-1.5 h-1.5 bg-blue-500 rounded-full" />
                        )}
                      </div>
                    </div>
                    
                    {/* Notification content */}
                    <div className="flex items-start justify-between gap-2 mt-1">
                      <p className={cn(
                        "text-xs text-gray-600 flex-1 min-w-0",
                        isExpanded ? "" : "line-clamp-2"
                      )}>
                        {notification.message}
                      </p>
                      
                      {/* Expand/collapse chevron for grouped notifications */}
                      <div className="flex items-center gap-1 flex-shrink-0">
                        {getPriorityBadge(notification.priority)}
                        {/* Chevron icon for grouped notifications or long messages */}
                        {(isGrouped || notification.message.length > 100) && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation()
                              toggleNotificationExpansion(notification.id)
                            }}
                            className="h-4 w-4 p-0 text-gray-500 hover:text-gray-700"
                          >
                            {isExpanded ? (
                              <ChevronUp className="h-3 w-3" />
                            ) : (
                              <ChevronDown className="h-3 w-3" />
                            )}
                          </Button>
                        )}
                      </div>
                    </div>

                    {/* Expanded Details */}
                    {isExpanded && (
                      <ExpandedNotificationDetails
                        notification={notification}
                        isGrouped={isGrouped}
                        getPanelNotificationIconProps={getPanelNotificationIconProps}
                        getPriorityBadge={getPriorityBadge}
                        formatDate={formatDate}
                        formatFullDate={formatFullDate}
                      />
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    </div>
  )
}