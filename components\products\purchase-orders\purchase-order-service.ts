import { getSupabaseClient } from '@/lib/supabase'
import { 
  PurchaseOrder, 
  PurchaseOrderItem, 
  ExtendedPurchaseOrder, 
  PurchaseOrderFormData,
  PurchaseOrderStatus
} from './types'
import { createSupabaseClient } from '@/lib/supabase'

// Get the current user's ID for fetching currency from profiles
async function getUserCurrency(organizationId: string) {
  const supabase = getSupabaseClient()
  
  try {
    // Get the organization owner's ID
    const { data: organization, error: orgError } = await supabase
      .from('organizations')
      .select('owner_id')
      .eq('id', organizationId)
      .single()
    
    if (orgError) {
      console.error('Error fetching organization:', orgError)
      return 'USD'
    }
    
    if (!organization?.owner_id) {
      console.warn('No organization owner found, using default currency USD')
      return 'USD'
    }
    
    // Get currency from organization owner's profile
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('currency')
      .eq('id', organization.owner_id)
      .single()
    
    if (profileError) {
      console.error('Error fetching owner currency:', profileError)
      return 'USD'
    }
    
    return profileData?.currency || 'USD'
  } catch (error) {
    console.error('Error in getUserCurrency:', error)
    return 'USD'
  }
}

// Get all purchase orders for an organization with optional filtering
export async function getPurchaseOrders(
  organizationId: string,
  filters?: {
    status?: PurchaseOrderStatus | 'all'
    supplier?: string
    search?: string
    dateRange?: {
      from?: string
      to?: string
    }
  }
): Promise<PurchaseOrder[]> {
  const supabase = getSupabaseClient()
  
  // Get organization's currency from the current user's profile
  const organizationCurrency = await getUserCurrency(organizationId)
  
  // Modified query to include item count
  let query = supabase
    .from('purchase_orders')
    .select(`
      *,
      item_count:purchase_order_items(count)
    `)
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: false })
  
  // Apply filters
  if (filters) {
    if (filters.status && filters.status !== 'all') {
      query = query.eq('status', filters.status)
    }
    
    if (filters.supplier) {
      query = query.ilike('supplier', `%${filters.supplier}%`)
    }
    
    if (filters.search) {
      query = query.or(`po_number.ilike.%${filters.search}%,supplier.ilike.%${filters.search}%`)
    }
    
    if (filters.dateRange) {
      if (filters.dateRange.from) {
        query = query.gte('issue_date', filters.dateRange.from)
      }
      if (filters.dateRange.to) {
        query = query.lte('issue_date', filters.dateRange.to)
      }
    }
  }
  
  const { data, error } = await query
  
  if (error) {
    console.error('Error fetching purchase orders:', error)
    throw new Error('Failed to fetch purchase orders')
  }
  
  return data.map(po => ({
    ...po,
    // Extract item count from the relationship
    item_count: po.item_count?.[0]?.count || 0,
    currency: po.currency || organizationCurrency, // Use purchase order currency or fallback to organization currency
    formatted_issue_date: formatDate(po.issue_date),
    formatted_expected_arrival_date: po.expected_arrival_date ? formatDate(po.expected_arrival_date) : null,
    formatted_received_date: po.received_date ? formatDate(po.received_date) : null,
    status_display: getStatusDisplay(po.status),
    status_color: getStatusColor(po.status)
  }))
}

// Get a single purchase order by ID with its items
export async function getPurchaseOrderById(
  id: string,
  organizationId: string
): Promise<ExtendedPurchaseOrder | null> {
  const supabase = getSupabaseClient()
  
  // Get organization's currency from the current user's profile
  const organizationCurrency = await getUserCurrency(organizationId)
  
  // Get the purchase order
  const { data: poData, error: poError } = await supabase
    .from('purchase_orders')
    .select('*')
    .eq('id', id)
    .eq('organization_id', organizationId)
    .single()
  
  if (poError) {
    console.error('Error fetching purchase order:', poError)
    return null
  }
  
  if (!poData) {
    return null
  }
  
  // Get the items for this purchase order
  const { data: itemsData, error: itemsError } = await supabase
    .from('purchase_order_items')
    .select('*')
    .eq('purchase_order_id', id)
    .eq('organization_id', organizationId)
    .order('created_at', { ascending: true })
  
  if (itemsError) {
    console.error('Error fetching purchase order items:', itemsError)
    throw new Error('Failed to fetch purchase order items')
  }
  
  const purchaseOrder: ExtendedPurchaseOrder = {
    ...poData,
    currency: poData.currency || organizationCurrency, // Ensure currency is included
    formatted_issue_date: formatDate(poData.issue_date),
    formatted_expected_arrival_date: poData.expected_arrival_date ? formatDate(poData.expected_arrival_date) : null,
    formatted_received_date: poData.received_date ? formatDate(poData.received_date) : null,
    status_display: getStatusDisplay(poData.status),
    status_color: getStatusColor(poData.status),
    items: itemsData
  }
  
  return purchaseOrder
}

// Create a new purchase order
export async function createPurchaseOrder(
  organizationId: string,
  formData: PurchaseOrderFormData
): Promise<PurchaseOrder> {
  const supabase = getSupabaseClient()
  
  // Get organization's currency from the current user's profile
  const organizationCurrency = await getUserCurrency(organizationId)
  
  // Validate required fields
  if (!formData.supplier.trim()) {
    throw new Error('Supplier is required')
  }
  
  if (formData.items.length === 0) {
    throw new Error('At least one item is required')
  }
  
  // Validate items
  for (const item of formData.items) {
    if (!item.productName.trim()) {
      throw new Error('Product name is required for all items')
    }
    
    if (item.quantity <= 0) {
      throw new Error('Quantity must be greater than zero')
    }
    
    if (item.unitCost < 0) {
      throw new Error('Unit cost cannot be negative')
    }
  }
  
  // Create the purchase order
  const { data: poData, error: poError } = await supabase
    .from('purchase_orders')
    .insert({
      organization_id: organizationId,
      supplier: formData.supplier,
      issue_date: formData.issue_date.toISOString(),
      expected_arrival_date: formData.expected_arrival_date?.toISOString() || null,
      notes: formData.notes,
      reference_number: formData.reference_number,
      currency: organizationCurrency, // Add organization's currency to the purchase order
      subtotal: formData.subtotal || 0,
      tax_rate: formData.tax_rate || 0,
      tax_amount: formData.tax_amount || 0,
      shipping_cost: formData.shipping_cost || 0,
      other_charges: formData.other_charges || 0,
      discount_amount: formData.discount_amount || 0,
      total_value: formData.total_value || 0
    })
    .select()
    .single()
  
  if (poError) {
    console.error('Error creating purchase order:', poError)
    throw new Error('Failed to create purchase order')
  }
  
  // Create the purchase order items
  if (formData.items.length > 0) {
    const itemsToInsert = formData.items.map(item => ({
      purchase_order_id: poData.id,
      organization_id: organizationId,
      product_id: item.productId,
      variant_id: item.variantId,
      product_name: item.productName,
      variant_name: item.variantName,
      sku: item.sku,
      quantity_ordered: item.quantity,
      quantity_received: 0,
      unit_cost: item.unitCost,
      variant_attributes: item.variantAttributes
    }))
    
    const { error: itemsError } = await supabase
      .from('purchase_order_items')
      .insert(itemsToInsert)
    
    if (itemsError) {
      console.error('Error creating purchase order items:', itemsError)
      // Try to delete the purchase order if items failed to create
      await supabase
        .from('purchase_orders')
        .delete()
        .eq('id', poData.id)
      
      throw new Error('Failed to create purchase order items')
    }
  }
  
  return {
    ...poData,
    currency: poData.currency || organizationCurrency, // Ensure currency is included in the response
    formatted_issue_date: formatDate(poData.issue_date),
    formatted_expected_arrival_date: poData.expected_arrival_date ? formatDate(poData.expected_arrival_date) : null,
    formatted_received_date: poData.received_date ? formatDate(poData.received_date) : null,
    status_display: getStatusDisplay(poData.status),
    status_color: getStatusColor(poData.status)
  }
}

// Create a draft product
export async function createDraftProduct(
  organizationId: string,
  productData: {
    name: string
    sku: string
    supplier: string
    base_cost: number
  }
): Promise<any> {
  const supabase = getSupabaseClient()
  
  // First, check if an active product with the same SKU already exists for this organization
  const { data: existingProduct, error: fetchError } = await supabase
    .from('products')
    .select('id')
    .eq('organization_id', organizationId)
    .eq('base_sku', productData.sku)
    .eq('is_active', true)  // Only check for active products
    .maybeSingle()
  
  // If there's an existing active product with the same SKU, return it
  if (existingProduct) {
    return existingProduct
  }
  
  // If there was an error other than "not found", throw it
  if (fetchError && fetchError.code !== 'PGRST116') { // PGRST116 is "JSON object requested, multiple (or no) rows returned"
    console.error('Error checking for existing product:', fetchError)
    throw new Error('Failed to check for existing product')
  }
  
  // Create the draft product
  const { data, error } = await supabase
    .from('products')
    .insert({
      organization_id: organizationId,
      name: productData.name,
      base_sku: productData.sku,
      supplier: productData.supplier,
      base_cost: productData.base_cost,
      has_variants: false,
      track_inventory: true,
      is_active: false, // Draft status
      price: null, // No selling price yet
      stock_quantity: 0,
      low_stock_threshold: 10
    })
    .select()
    .single()
  
  if (error) {
    console.error('Error creating draft product:', error)
    throw new Error('Failed to create draft product')
  }
  
  return data
}

// Update an existing purchase order
export async function updatePurchaseOrder(
  id: string,
  organizationId: string,
  updates: Partial<PurchaseOrder>
): Promise<PurchaseOrder> {
  const supabase = getSupabaseClient()
  
  // Validate updates if supplier is being changed
  if (updates.supplier !== undefined && !updates.supplier.trim()) {
    throw new Error('Supplier is required')
  }
  
  const { data, error } = await supabase
    .from('purchase_orders')
    .update(updates)
    .eq('id', id)
    .eq('organization_id', organizationId)
    .select()
    .single()
  
  if (error) {
    console.error('Error updating purchase order:', error)
    throw new Error('Failed to update purchase order')
  }
  
  return {
    ...data,
    formatted_issue_date: formatDate(data.issue_date),
    formatted_expected_arrival_date: data.expected_arrival_date ? formatDate(data.expected_arrival_date) : null,
    formatted_received_date: data.received_date ? formatDate(data.received_date) : null,
    status_display: getStatusDisplay(data.status),
    status_color: getStatusColor(data.status)
  }
}

// Update purchase order item received quantity
export async function updatePurchaseOrderItemReceived(
  itemId: string,
  organizationId: string,
  quantityReceived: number
): Promise<PurchaseOrderItem> {
  const supabase = getSupabaseClient()
  
  // Validate quantity
  if (quantityReceived < 0) {
    throw new Error('Quantity received cannot be negative')
  }
  
  // First, get the current item to compare quantities
  const { data: currentItem, error: fetchError } = await supabase
    .from('purchase_order_items')
    .select('*')
    .eq('id', itemId)
    .eq('organization_id', organizationId)
    .single()
  
  if (fetchError) {
    console.error('Error fetching purchase order item:', fetchError)
    throw new Error('Failed to fetch purchase order item')
  }
  
  // Validate that received quantity doesn't exceed ordered quantity
  if (quantityReceived > currentItem.quantity_ordered) {
    throw new Error('Quantity received cannot exceed quantity ordered')
  }
  
  // Update the item
  const { data, error } = await supabase
    .from('purchase_order_items')
    .update({ quantity_received: quantityReceived })
    .eq('id', itemId)
    .eq('organization_id', organizationId)
    .select()
    .single()
  
  if (error) {
    console.error('Error updating purchase order item:', error)
    throw new Error('Failed to update purchase order item')
  }
  
  // If quantity received increased, log to stock history
  const quantityDifference = quantityReceived - currentItem.quantity_received
  if (quantityDifference > 0) {
    await logStockAdjustmentForPurchaseOrder(
      organizationId,
      currentItem,
      quantityDifference
    )
  }
  
  return data
}

// Delete a purchase order
export async function deletePurchaseOrder(
  id: string,
  organizationId: string
): Promise<void> {
  const supabase = getSupabaseClient()
  
  // Delete items first (due to foreign key constraint)
  const { error: itemsError } = await supabase
    .from('purchase_order_items')
    .delete()
    .eq('purchase_order_id', id)
    .eq('organization_id', organizationId)
  
  if (itemsError) {
    console.error('Error deleting purchase order items:', itemsError)
    throw new Error('Failed to delete purchase order items')
  }
  
  // Delete the purchase order
  const { error: poError } = await supabase
    .from('purchase_orders')
    .delete()
    .eq('id', id)
    .eq('organization_id', organizationId)
  
  if (poError) {
    console.error('Error deleting purchase order:', poError)
    throw new Error('Failed to delete purchase order')
  }
}

// Update purchase order status
export async function updatePurchaseOrderStatus(
  id: string,
  organizationId: string,
  status: PurchaseOrderStatus
): Promise<PurchaseOrder> {
  const supabase = getSupabaseClient()
  
  const { data, error } = await supabase
    .from('purchase_orders')
    .update({ status })
    .eq('id', id)
    .eq('organization_id', organizationId)
    .select()
    .single()
  
  if (error) {
    console.error('Error updating purchase order status:', error)
    throw new Error('Failed to update purchase order status')
  }
  
  return {
    ...data,
    formatted_issue_date: formatDate(data.issue_date),
    formatted_expected_arrival_date: data.expected_arrival_date ? formatDate(data.expected_arrival_date) : null,
    formatted_received_date: data.received_date ? formatDate(data.received_date) : null,
    status_display: getStatusDisplay(data.status),
    status_color: getStatusColor(data.status)
  }
}

// Get unique suppliers for an organization
export async function getSuppliers(organizationId: string): Promise<string[]> {
  const supabase = getSupabaseClient()
  
  // Get suppliers from purchase orders
  const { data: poData, error: poError } = await supabase
    .from('purchase_orders')
    .select('supplier')
    .eq('organization_id', organizationId)
    .order('supplier')
  
  if (poError) {
    console.error('Error fetching suppliers from purchase orders:', poError)
    // Don't throw error, continue to get suppliers from products
  }
  
  // Get suppliers from products
  const { data: productData, error: productError } = await supabase
    .from('products')
    .select('supplier')
    .eq('organization_id', organizationId)
    .order('supplier')
  
  if (productError) {
    console.error('Error fetching suppliers from products:', productError)
    // If both fail, throw error
    if (poError) {
      throw new Error('Failed to fetch suppliers')
    }
  }
  
  // Extract unique suppliers from both sources
  const poSuppliers = poData ? poData.map(po => po.supplier) : [];
  const productSuppliers = productData ? productData.map(p => p.supplier) : [];
  const allSuppliers = Array.from(new Set([...poSuppliers, ...productSuppliers]))
    .filter(supplier => supplier !== null && supplier !== undefined) as string[];
    
  return allSuppliers;
}

// Helper function to format dates for display
function formatDate(dateString: string): string {
  // Handle undefined or null dateString
  if (!dateString) {
    return 'Unknown date'
  }
  
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString()
  } catch (error) {
    console.error('Error formatting date:', error, 'dateString:', dateString)
    return 'Invalid date'
  }
}

// Helper function to get status display text
function getStatusDisplay(status: PurchaseOrderStatus): string {
  switch (status) {
    case 'draft': return 'Draft'
    case 'open': return 'Open'
    case 'partially_received': return 'Partially Received'
    case 'received': return 'Received'
    case 'cancelled': return 'Cancelled'
    default: return status
  }
}

// Helper function to get status color
function getStatusColor(status: PurchaseOrderStatus): string {
  switch (status) {
    case 'draft': return 'bg-gray-100 text-gray-800'
    case 'open': return 'bg-blue-100 text-blue-800'
    case 'partially_received': return 'bg-yellow-100 text-yellow-800'
    case 'received': return 'bg-green-100 text-green-800'
    case 'cancelled': return 'bg-red-100 text-red-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// Helper function to log stock adjustment for purchase order
async function logStockAdjustmentForPurchaseOrder(
  organizationId: string,
  item: PurchaseOrderItem,
  quantity: number
): Promise<void> {
  const supabase = getSupabaseClient()
  
  try {
    // Get user display name for logging
    const { data: userData } = await supabase
      .from('profiles')
      .select('display_name, email')
      .eq('id', organizationId)
      .single()
    
    const userName = userData?.display_name || userData?.email || 'Unknown User'
    
    // Get the purchase order for reference
    const { data: poData } = await supabase
      .from('purchase_orders')
      .select('po_number')
      .eq('id', item.purchase_order_id)
      .single()
    
    // Get current stock quantity for the product/variant
    let currentQuantity = 0
    if (item.product_id) {
      const { data: productData } = await supabase
        .from('products')
        .select('stock_quantity')
        .eq('id', item.product_id)
        .single()
      currentQuantity = productData?.stock_quantity || 0
    } else if (item.variant_id) {
      const { data: variantData } = await supabase
        .from('product_variants')
        .select('stock_quantity')
        .eq('id', item.variant_id)
        .single()
      currentQuantity = variantData?.stock_quantity || 0
    }
    
    // Log to stock history
    const { error: logError } = await supabase
      .from('stock_history')
      .insert({
        user_id: organizationId,
        product_id: item.product_id,
        variant_id: item.variant_id,
        change_type: 'purchase_order',
        change_reason: 'shipment_received',
        change_notes: `Received ${quantity} units from purchase order ${poData?.po_number || item.purchase_order_id}`,
        previous_quantity: currentQuantity,
        new_quantity: currentQuantity + quantity,
        quantity_change: quantity,
        source_type: 'purchase_order',
        source_id: item.purchase_order_id,
        source_reference: poData?.po_number || item.purchase_order_id,
        created_by: organizationId,
        created_by_name: userName
      })
    
    if (logError) {
      console.error('Error logging stock adjustment:', logError)
      // Don't throw error here as this is just logging
    }
    
    // Update the actual stock quantity in the product/variant
    // Also activate draft products when they are received
    if (item.product_id) {
      const updateData: any = { 
        stock_quantity: currentQuantity + quantity,
        updated_at: new Date().toISOString()
      };
      
      // Check if this is a draft product that needs to be activated
      const { data: productData } = await supabase
        .from('products')
        .select('is_active, price')
        .eq('id', item.product_id)
        .single();
      
      if (productData && !productData.is_active) {
        updateData.is_active = true;
        // Set a default price of 0 if not already set, so the product can be activated
        // The user will need to edit the product to set a proper price
        if (updateData.price == null) {
          updateData.price = 0;
        }
      }
      
      await supabase
        .from('products')
        .update(updateData)
        .eq('id', item.product_id)
    } else if (item.variant_id) {
      await supabase
        .from('product_variants')
        .update({ 
          stock_quantity: currentQuantity + quantity,
          updated_at: new Date().toISOString()
        })
        .eq('id', item.variant_id)
    }
  } catch (error) {
    console.error('Error in logStockAdjustmentForPurchaseOrder:', error)
    // Don't throw error here as this is just logging
  }
}