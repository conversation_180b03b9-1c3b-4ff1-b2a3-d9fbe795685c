'use client'

import React, { useEffect, useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Plus, Building2, Tag } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { useAuth } from '@/contexts/auth-context'
import { designSystem } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'
import { SimpleDropdown } from '@/components/ui/simple-dropdown'
import { createSupabaseClient } from '@/lib/supabase'
import { useToast } from '@/components/ui/use-toast'

interface Category {
  id: string
  name: string
}

interface Supplier {
  id: string
  name: string
}

export function CategorySupplierForm() {
  const { formData, updateFormData, errors, categories, setCategories, suppliers, setSuppliers } = useProductForm()
  const { organizationId } = useAuth()
  const { toast } = useToast()

  // Load categories and suppliers on mount
  useEffect(() => {
    if (organizationId) {
      loadCategories()
      loadSuppliers()
    }
  }, [organizationId])

  const loadCategories = async () => {
    if (!organizationId) return
    
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('categories')
        .select('id, name')
        .eq('organization_id', organizationId)
        .order('name')

      if (error) throw error
      setCategories(data || [])
    } catch (error) {
      console.error('Error loading categories:', error)
      toast({
        title: "Error",
        description: "Failed to load categories",
        variant: "destructive"
      })
    }
  }

  const loadSuppliers = async () => {
    if (!organizationId) return
    
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('suppliers')
        .select('id, name')
        .eq('organization_id', organizationId)
        .order('name')

      if (error) throw error
      setSuppliers(data || [])
    } catch (error) {
      console.error('Error loading suppliers:', error)
      toast({
        title: "Error",
        description: "Failed to load suppliers",
        variant: "destructive"
      })
    }
  }

  const createCategory = async (name: string) => {
    if (!organizationId) return null
    
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('categories')
        .insert({
          name: name.trim(),
          organization_id: organizationId
        })
        .select('id, name')
        .single()

      if (error) throw error
      
      // Add to local state
      setCategories(prev => [...prev, data])
      
      toast({
        title: "Category Created",
        description: `Category "${name}" created successfully`
      })
      
      return data.id
    } catch (error) {
      console.error('Error creating category:', error)
      toast({
        title: "Error",
        description: "Failed to create category",
        variant: "destructive"
      })
      return null
    }
  }

  const createSupplier = async (name: string) => {
    if (!organizationId) return null
    
    try {
      const supabase = createSupabaseClient()
      const { data, error } = await supabase
        .from('suppliers')
        .insert({
          name: name.trim(),
          organization_id: organizationId
        })
        .select('id, name')
        .single()

      if (error) throw error
      
      // Add to local state
      setSuppliers(prev => [...prev, data])
      
      toast({
        title: "Supplier Created",
        description: `Supplier "${name}" created successfully`
      })
      
      return data.id
    } catch (error) {
      console.error('Error creating supplier:', error)
      toast({
        title: "Error",
        description: "Failed to create supplier",
        variant: "destructive"
      })
      return null
    }
  }

  // Convert categories and suppliers to dropdown format
  const categoryOptions = categories.map(cat => ({ value: cat.id, label: cat.name }))
  const supplierOptions = suppliers.map(sup => ({ value: sup.id, label: sup.name }))

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Categorization
        </h3>
        <p className={designSystem.typography.caption}>
          Organize your product with categories and supplier information
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Category Selection */}
        <FormField
          label="Category"
          error={errors.category_id}
        >
          <SimpleDropdown
            options={categoryOptions}
            value={formData.category_id}
            onValueChange={(value) => updateFormData('category_id', value)}
            placeholder="Select category"
            allowCustom={true}
            onAddCustomOption={async (option) => {
              const id = await createCategory(option.label);
              return id ? { ...option, value: id } : option;
            }}
            size="xs" // Set compact size
            textsize="xs" // Set xs text size
          />
        </FormField>

        {/* Supplier Selection */}
        <FormField
          label="Supplier"
          error={errors.supplier}
        >
          <SimpleDropdown
            options={supplierOptions}
            value={formData.supplier}
            onValueChange={(value) => updateFormData('supplier', value)}
            placeholder="Select supplier"
            allowCustom={true}
            onAddCustomOption={async (option) => {
              const id = await createSupplier(option.label);
              return id ? { ...option, value: id } : option;
            }}
            size="xs" // Set compact size
            textsize="xs" // Set xs text size
          />
        </FormField>
      </div>
    </div>
  )
}