'use client'

import { createContext, useContext, ReactNode } from 'react'

interface NotificationContextType {
  // State
  notifications: any[]
  filteredNotifications: any[]
  loading: boolean
  readFilter: 'all' | 'unread' | 'read' | 'archived'
  typeFilter: string
  searchQuery: string
  currentPage: number
  totalNotifications: number
  selectedNotifications: string[]
  expandedNotifications: string[]
  highlightedNotification: string | null
  viewMode: 'list' | 'cards' | 'grouped'
  sortBy: 'date' | 'priority' | 'type'
  sortOrder: 'asc' | 'desc'
  showFilters: boolean
  notificationsPerPage: number
  
  // Functions
  setReadFilter: (filter: 'all' | 'unread' | 'read' | 'archived') => void
  setTypeFilter: (filter: string) => void
  setSearchQuery: (query: string) => void
  setCurrentPage: (page: number) => void
  setTotalNotifications: (count: number) => void
  setSelectedNotifications: (ids: string[]) => void
  setExpandedNotifications: (ids: string[]) => void
  setHighlightedNotification: (id: string | null) => void
  setViewMode: (mode: 'list' | 'cards' | 'grouped') => void
  setSortBy: (sort: 'date' | 'priority' | 'type') => void
  setSortOrder: (order: 'asc' | 'desc') => void
  setShowFilters: (show: boolean) => void
  toggleNotificationSelection: (id: string) => void
  toggleSelectAll: () => void
  toggleNotificationExpansion: (id: string) => void
  markAsRead: (id: string) => Promise<void>
  markAsUnread: (id: string) => Promise<void>
  archiveNotification: (id: string) => Promise<void>
  deleteNotification: (id: string) => Promise<void>
  markAllAsRead: () => Promise<void>
  archiveSelected: () => Promise<void>
  deleteSelected: () => Promise<void>
  loadNotifications: () => Promise<void>
  formatDate: (dateString: string) => string
  formatFullDate: (dateString: string) => string
  getPanelNotificationIconProps: (type: string) => {
    Icon: any
    bgClass: string
    iconClass: string
  }
  getPriorityBadge: (priority?: string) => JSX.Element | null
  groupRelatedNotifications: (notifications: any[]) => any[]
  sortNotifications: (notifications: any[]) => any[]
  groupNotifications: (notifications: any[]) => any[]
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({
  children,
  value
}: {
  children: ReactNode
  value: NotificationContextType
}) {
  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  )
}

export function useNotification() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotification must be used within a NotificationProvider')
  }
  return context
}