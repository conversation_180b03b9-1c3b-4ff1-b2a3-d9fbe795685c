'use client'

import React, { useState } from 'react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { ChevronDown, ChevronRight, DollarSign, Package, BarChart3, Copy } from 'lucide-react'
import { useProductForm } from '../../../context/ProductFormContext'
import { designSystem, getInputClasses, getButtonClasses, getBadgeClasses } from '../../../config/design-system'
import { FormField } from '../../shared/FormField'

export function VariantConfigurator() {
  const { savedVariants, setSavedVariants } = useProductForm()
  const [expandedVariants, setExpandedVariants] = useState<Set<string>>(new Set())
  const [bulkValues, setBulkValues] = useState({
    base_cost: '',
    packaging_cost: '',
    price: '',
    quantity: '',
    low_stock_threshold: ''
  })

  // Toggle variant expansion
  const toggleVariant = (variantId: string) => {
    const newExpanded = new Set(expandedVariants)
    if (newExpanded.has(variantId)) {
      newExpanded.delete(variantId)
    } else {
      newExpanded.add(variantId)
    }
    setExpandedVariants(newExpanded)
  }

  // Update variant data
  const updateVariant = (variantId: string, field: string, value: any) => {
    setSavedVariants(prev => prev.map(variant =>
      variant.id === variantId
        ? { ...variant, [field]: value }
        : variant
    ))
  }

  // Apply bulk values to all variants
  const applyBulkValues = () => {
    setSavedVariants(prev => prev.map(variant => ({
      ...variant,
      ...(bulkValues.base_cost && { base_cost: parseFloat(bulkValues.base_cost) || null }),
      ...(bulkValues.packaging_cost && { packaging_cost: parseFloat(bulkValues.packaging_cost) || null }),
      ...(bulkValues.price && { price: parseFloat(bulkValues.price) || null }),
      ...(bulkValues.quantity && { quantity: parseInt(bulkValues.quantity) || 0 }),
      ...(bulkValues.low_stock_threshold && { low_stock_threshold: parseInt(bulkValues.low_stock_threshold) || 5 })
    })))

    // Clear bulk values
    setBulkValues({
      base_cost: '',
      packaging_cost: '',
      price: '',
      quantity: '',
      low_stock_threshold: ''
    })
  }

  // Copy values from one variant to another
  const copyVariantValues = (sourceId: string, targetId: string) => {
    const sourceVariant = savedVariants.find(v => v.id === sourceId)
    if (!sourceVariant) return

    setSavedVariants(prev => prev.map(variant =>
      variant.id === targetId
        ? {
            ...variant,
            price: sourceVariant.price,
            base_cost: sourceVariant.base_cost,
            packaging_cost: sourceVariant.packaging_cost,
            quantity: sourceVariant.quantity,
            low_stock_threshold: sourceVariant.low_stock_threshold
          }
        : variant
    ))
  }

  // Calculate profit for variant
  const calculateVariantProfit = (variant: any) => {
    const totalCost = (variant.base_cost || 0) + (variant.packaging_cost || 0)
    const price = variant.price || 0
    if (totalCost === 0 || price === 0) return { profit: 0, margin: 0 }
    
    const profit = price - totalCost
    const margin = (profit / price) * 100
    return { profit, margin }
  }

  if (savedVariants.length === 0) {
    return (
      <div className="text-center py-8">
        <Package className="w-12 h-12 mx-auto mb-4 text-gray-400" />
        <p className={designSystem.typography.body}>
          No variants generated yet
        </p>
        <p className={designSystem.typography.caption}>
          Generate variants first to configure pricing and inventory
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className={designSystem.typography.subsectionTitle}>
          Variant Configuration
        </h3>
        <p className={designSystem.typography.caption}>
          Configure pricing, costs, and inventory for each product variant
        </p>
      </div>

      {/* Bulk Operations */}
      <Card>
        <CardContent className="p-4">
          <h4 className={`${designSystem.typography.label} mb-4`}>
            Bulk Operations
          </h4>
          
          <div className="grid grid-cols-2 md:grid-cols-5 gap-3 mb-4">
            <FormField label="Base Cost">
              <div className="relative">
                <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                <Input
                  type="number"
                  value={bulkValues.base_cost}
                  onChange={(e) => setBulkValues(prev => ({ ...prev, base_cost: e.target.value }))}
                  placeholder="0.00"
                  className={`${getInputClasses()} pl-5`}
                  step="0.01"
                />
              </div>
            </FormField>

            <FormField label="Packaging">
              <div className="relative">
                <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                <Input
                  type="number"
                  value={bulkValues.packaging_cost}
                  onChange={(e) => setBulkValues(prev => ({ ...prev, packaging_cost: e.target.value }))}
                  placeholder="0.00"
                  className={`${getInputClasses()} pl-5`}
                  step="0.01"
                />
              </div>
            </FormField>

            <FormField label="Price">
              <div className="relative">
                <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                <Input
                  type="number"
                  value={bulkValues.price}
                  onChange={(e) => setBulkValues(prev => ({ ...prev, price: e.target.value }))}
                  placeholder="0.00"
                  className={`${getInputClasses()} pl-5`}
                  step="0.01"
                />
              </div>
            </FormField>

            <FormField label="Stock">
              <Input
                type="number"
                value={bulkValues.quantity}
                onChange={(e) => setBulkValues(prev => ({ ...prev, quantity: e.target.value }))}
                placeholder="0"
                className={getInputClasses()}
              />
            </FormField>

            <FormField label="Low Stock">
              <Input
                type="number"
                value={bulkValues.low_stock_threshold}
                onChange={(e) => setBulkValues(prev => ({ ...prev, low_stock_threshold: e.target.value }))}
                placeholder="5"
                className={getInputClasses()}
              />
            </FormField>
          </div>

          <Button
            type="button"
            onClick={applyBulkValues}
            disabled={!Object.values(bulkValues).some(v => v.trim())}
            className={getButtonClasses('primary')}
          >
            Apply to All Variants
          </Button>
        </CardContent>
      </Card>

      {/* Variants List */}
      <div className="space-y-3">
        {savedVariants.map((variant, index) => {
          const isExpanded = expandedVariants.has(variant.id)
          const { profit, margin } = calculateVariantProfit(variant)
          
          return (
            <Card key={variant.id}>
              <Collapsible>
                <CollapsibleTrigger asChild>
                  <div
                    className="w-full p-4 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => toggleVariant(variant.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        {isExpanded ? (
                          <ChevronDown className="w-4 h-4 text-gray-500" />
                        ) : (
                          <ChevronRight className="w-4 h-4 text-gray-500" />
                        )}
                        
                        <div className="flex items-center gap-2">
                          {variant.attributes.map((attr, i) => (
                            <Badge key={i} className={getBadgeClasses('secondary')}>
                              {attr.name}: {attr.value}
                            </Badge>
                          ))}
                        </div>
                      </div>

                      <div className="flex items-center gap-4 text-sm">
                        <div className="text-right">
                          <p className="font-medium">
                            {variant.price ? `$${variant.price.toFixed(2)}` : 'No price'}
                          </p>
                          <p className={designSystem.typography.caption}>
                            Stock: {variant.quantity}
                          </p>
                        </div>
                        
                        {variant.price && profit > 0 && (
                          <Badge className={getBadgeClasses(margin >= 30 ? 'success' : margin >= 15 ? 'warning' : 'error')}>
                            {margin.toFixed(1)}% margin
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CollapsibleTrigger>

                <CollapsibleContent>
                  <div className="px-4 pb-4 border-t border-gray-200">
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4">
                      {/* Pricing Section */}
                      <div className="space-y-3">
                        <h5 className={`${designSystem.typography.label} flex items-center gap-2`}>
                          <DollarSign className="w-4 h-4" />
                          Pricing
                        </h5>
                        
                        <FormField label="Base Cost">
                          <div className="relative">
                            <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                            <Input
                              type="number"
                              value={variant.base_cost || ''}
                              onChange={(e) => updateVariant(variant.id, 'base_cost', parseFloat(e.target.value) || null)}
                              placeholder="0.00"
                              className={`${getInputClasses()} pl-5`}
                              step="0.01"
                            />
                          </div>
                        </FormField>

                        <FormField label="Packaging Cost">
                          <div className="relative">
                            <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                            <Input
                              type="number"
                              value={variant.packaging_cost || ''}
                              onChange={(e) => updateVariant(variant.id, 'packaging_cost', parseFloat(e.target.value) || null)}
                              placeholder="0.00"
                              className={`${getInputClasses()} pl-5`}
                              step="0.01"
                            />
                          </div>
                        </FormField>

                        <FormField label="Selling Price">
                          <div className="relative">
                            <span className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 text-xs">$</span>
                            <Input
                              type="number"
                              value={variant.price || ''}
                              onChange={(e) => updateVariant(variant.id, 'price', parseFloat(e.target.value) || null)}
                              placeholder="0.00"
                              className={`${getInputClasses()} pl-5`}
                              step="0.01"
                            />
                          </div>
                        </FormField>
                      </div>

                      {/* Inventory Section */}
                      <div className="space-y-3">
                        <h5 className={`${designSystem.typography.label} flex items-center gap-2`}>
                          <Package className="w-4 h-4" />
                          Inventory
                        </h5>
                        
                        <FormField label="Stock Quantity">
                          <Input
                            type="number"
                            value={variant.quantity}
                            onChange={(e) => updateVariant(variant.id, 'quantity', parseInt(e.target.value) || 0)}
                            placeholder="0"
                            className={getInputClasses()}
                          />
                        </FormField>

                        <FormField label="Low Stock Threshold">
                          <Input
                            type="number"
                            value={variant.low_stock_threshold}
                            onChange={(e) => updateVariant(variant.id, 'low_stock_threshold', parseInt(e.target.value) || 5)}
                            placeholder="5"
                            className={getInputClasses()}
                          />
                        </FormField>

                        <FormField label="SKU">
                          <Input
                            value={variant.sku}
                            onChange={(e) => updateVariant(variant.id, 'sku', e.target.value)}
                            placeholder="SKU"
                            className={getInputClasses()}
                          />
                        </FormField>
                      </div>

                      {/* Analytics Section */}
                      <div className="space-y-3">
                        <h5 className={`${designSystem.typography.label} flex items-center gap-2`}>
                          <BarChart3 className="w-4 h-4" />
                          Analytics
                        </h5>
                        
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className={designSystem.typography.caption}>Total Cost:</span>
                            <span className="font-medium">
                              ${((variant.base_cost || 0) + (variant.packaging_cost || 0)).toFixed(2)}
                            </span>
                          </div>
                          
                          <div className="flex justify-between">
                            <span className={designSystem.typography.caption}>Profit:</span>
                            <span className={`font-medium ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                              ${profit.toFixed(2)}
                            </span>
                          </div>
                          
                          <div className="flex justify-between">
                            <span className={designSystem.typography.caption}>Margin:</span>
                            <span className={`font-medium ${margin >= 15 ? 'text-green-600' : 'text-red-600'}`}>
                              {margin.toFixed(1)}%
                            </span>
                          </div>
                        </div>

                        {/* Copy Actions */}
                        <div className="pt-2 border-t border-gray-200">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => {
                              // Copy to next variant if exists
                              const nextIndex = index + 1
                              if (nextIndex < savedVariants.length) {
                                copyVariantValues(variant.id, savedVariants[nextIndex].id)
                              }
                            }}
                            disabled={index === savedVariants.length - 1}
                            className="w-full text-xs"
                          >
                            <Copy className="w-3 h-3 mr-1" />
                            Copy to Next
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </Card>
          )
        })}
      </div>
    </div>
  )
}
