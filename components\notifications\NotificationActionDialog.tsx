'use client'

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Trash2, Archive, EyeOff } from 'lucide-react'

interface NotificationActionDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  action: 'delete' | 'archive' | 'mark-unread'
  notificationTitle: string
  onConfirm: () => void
  isProcessing?: boolean
}

export function NotificationActionDialog({
  open,
  onOpenChange,
  action,
  notificationTitle,
  onConfirm,
  isProcessing = false,
}: NotificationActionDialogProps) {
  const getActionDetails = () => {
    switch (action) {
      case 'delete':
        return {
          title: 'Delete Notification',
          description: `Are you sure you want to permanently delete "${notificationTitle}"? This action cannot be undone.`,
          confirmText: 'Delete',
          icon: <Trash2 className="h-4 w-4" />,
          variant: 'destructive' as const,
        }
      case 'archive':
        return {
          title: 'Archive Notification',
          description: `Are you sure you want to archive "${notificationTitle}"? You can find it later in the archived notifications.`,
          confirmText: 'Archive',
          icon: <Archive className="h-4 w-4" />,
          variant: 'default' as const,
        }
      case 'mark-unread':
        return {
          title: 'Mark as Unread',
          description: `Mark "${notificationTitle}" as unread? It will appear as a new notification.`,
          confirmText: 'Mark as Unread',
          icon: <EyeOff className="h-4 w-4" />,
          variant: 'default' as const,
        }
      default:
        return {
          title: 'Confirm Action',
          description: `Are you sure you want to perform this action on "${notificationTitle}"?`,
          confirmText: 'Confirm',
          icon: <AlertTriangle className="h-4 w-4" />,
          variant: 'default' as const,
        }
    }
  }

  const { title, description, confirmText, icon, variant } = getActionDetails()

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {icon}
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogFooter className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            Cancel
          </Button>
          <Button
            variant={variant}
            onClick={onConfirm}
            disabled={isProcessing}
          >
            {isProcessing && (
              <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
            )}
            {confirmText}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}